import React, { Suspense, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, useGLTF } from '@react-three/drei';

function Model({ modelPath, onError }) {
  try {
    const { scene } = useGLTF(modelPath);
    return <primitive object={scene} scale={1.5} />;
  } catch (error) {
    console.error('Error loading GLTF model:', error);
    onError && onError(error);
    // Return a fallback cube
    return (
      <mesh>
        <boxGeometry args={[2, 2, 2]} />
        <meshStandardMaterial color="#666" />
      </mesh>
    );
  }
}

function ModelViewer() {
  const { modelName } = useParams(); // Dynamically get the model name
  const modelPath = `/${modelName}.glb`; // Build model path from URL param
  const [modelError, setModelError] = useState(false);

  const handleModelError = (error) => {
    console.error('Model loading failed:', error);
    setModelError(true);
  };

  return (
    <div>
      <h2>3D Model Viewer</h2>
      {modelError && (
        <div style={{ color: 'red', padding: '10px' }}>
          Failed to load 3D model. The model file may not exist or is corrupted.
        </div>
      )}
      <Suspense fallback={<div>Loading 3D Model...</div>}>
        <Canvas style={{ height: '500px', width: '100%' }}>
          <ambientLight intensity={0.5} />
          <directionalLight position={[2, 2, 2]} />
          <OrbitControls />
          <Model modelPath={modelPath} onError={handleModelError} />
        </Canvas>
      </Suspense>
    </div>
  );
}

export default ModelViewer;
