/* ===== NEW ABOUT PAGE DESIGN ===== */

/* Main Container */
.new-about-container {
  min-height: 100vh;
  padding: 80px 20px 20px;
  position: relative;
  overflow-x: hidden;
}

.new-about-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(168, 86, 245, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(85, 4, 247, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Ensure mobile page title is hidden */
.new-about-container .mobile-page-title {
  display: none !important;
}

/* FORCE pageTitle to stay FIXED on About page - MAXIMUM SPECIFICITY */
html body div.new-about-container ~ div.pageTitle,
html body div.new-about-container ~ .pageTitle,
html body .new-about-container ~ div.pageTitle,
html body .new-about-container ~ .pageTitle,
html body div.pageTitle,
html body .pageTitle,
body div.pageTitle,
body .pageTitle,
div.pageTitle,
.pageTitle,
*[class="pageTitle"],
[class="pageTitle"] {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 99999 !important;
  display: block !important;
  visibility: visible !important;
}

/* Hero Section */
.about-hero-section {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.hero-content-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

.about-main-title {
  font-size: clamp(2.5rem, 8vw, 4.5rem);
  font-weight: 900;
  margin-bottom: 20px;
  line-height: 1.1;
  text-align: center;
}

.title-gradient {
  background: linear-gradient(135deg, #5504f7, #a855f7, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  margin-right: 20px;
}

.title-accent {
  background: linear-gradient(135deg, #a855f7, #d946ef, #f97316);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
  display: inline-block;
  filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.4));
  animation: gradientShift 3s ease-in-out infinite;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 300;
  letter-spacing: 0.5px;
}

/* Content Grid */
.about-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto 60px;
}

/* Card Base Styles */
.about-card {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.about-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(168, 86, 245, 0.1), rgba(255, 215, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.about-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(168, 86, 245, 0.3);
  border-color: rgba(168, 86, 245, 0.5);
}

.about-card:hover::before {
  opacity: 1;
}

/* Specific Card Styling */
.vision-card {
  border-left: 4px solid #5504f7;
}

.vision-card:hover {
  box-shadow: 0 20px 40px rgba(85, 4, 247, 0.3);
}

.technology-card {
  border-left: 4px solid #a855f7;
}

.technology-card:hover {
  box-shadow: 0 20px 40px rgba(168, 85, 247, 0.3);
}

.technology-card .tech-features {
  background: transparent;
  padding: 0;
  border-radius: 0;
  border: none;
  margin-top: 20px;
}

.contact-card {
  border-left: 4px solid #d946ef;
}

.contact-card:hover {
  box-shadow: 0 20px 40px rgba(217, 70, 239, 0.3);
}

/* Card Headers */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
  z-index: 2;
}

.card-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.card-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 0 10px rgba(168, 86, 245, 0.5);
}

/* Card Content */
.card-content {
  position: relative;
  z-index: 2;
}

.card-content p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

/* Technology Features */
.tech-features {
  margin-top: 25px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.feature-item:hover {
  background: rgba(168, 86, 245, 0.2);
  transform: translateX(5px);
  border-color: rgba(168, 86, 245, 0.4);
  box-shadow: 0 5px 15px rgba(168, 86, 245, 0.2);
}

.feature-icon {
  font-size: 1.4rem;
  margin-right: 15px;
  filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.7));
  color: #a855f7;
}

.feature-item span:last-child {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  font-size: 1.05rem;
}

/* Developer Info */
.developer-info {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
}

.developer-name {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #5504f7, #a855f7, #d946ef);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  text-shadow: 0 0 15px rgba(168, 85, 247, 0.6);
  filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.4));
  animation: gradientShift 4s ease-in-out infinite;
}

.developer-role {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-style: italic;
}

/* Contact Methods */
.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-method {
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  min-height: 60px;
}

.contact-method:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
  border-color: rgba(168, 86, 245, 0.3);
}

.contact-method:hover .method-icon {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.method-icon {
  width: 50px;
  height: 50px;
  min-width: 50px;
  min-height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.method-icon i {
  color: white !important;
  font-size: 1.5rem !important;
}

.linkedin .method-icon {
  background: linear-gradient(135deg, #0077b5, #00a0dc);
}

.instagram .method-icon {
  background: linear-gradient(135deg, #e4405f, #f77737, #fcaf45);
}

.email .method-icon {
  background: linear-gradient(135deg, #ea4335, #fbbc05);
  color: white;
}

.email .method-icon i {
  color: white !important;
  font-size: 1.5rem;
}

/* Specific styling for email contact method */
.contact-method.email {
  align-items: center;
}

.contact-method.email .method-info {
  flex: 1;
  min-width: 0;
}

.contact-method.email .method-value {
  max-width: 180px;
}

.method-info {
  display: flex;
  flex-direction: column;
}

.method-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.method-value {
  font-size: 1.1rem;
  color: white;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Stats Section */
.about-stats-section {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-item {
  text-align: center;
  padding: 30px 20px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(168, 86, 245, 0.3);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(168, 86, 245, 0.2);
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(135deg, #5504f7, #a855f7, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .new-about-container.tablet {
    padding: 80px 30px 30px;
  }

  .about-content-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .about-card {
    padding: 25px;
  }

  .card-title {
    font-size: 1.6rem;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .new-about-container.mobile {
    padding: 80px 15px 20px;
  }

  .about-hero-section {
    margin-bottom: 40px;
    padding: 20px 0;
  }

  .about-main-title {
    font-size: clamp(2rem, 10vw, 3rem);
    margin-bottom: 15px;
  }

  .title-gradient,
  .title-accent {
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .hero-subtitle {
    font-size: clamp(1rem, 4vw, 1.2rem);
  }

  .about-content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
  }

  .about-card {
    padding: 20px;
    border-radius: 20px;
  }

  .card-header {
    flex-direction: column;
    text-align: center;
    margin-bottom: 20px;
  }

  .card-icon {
    font-size: 2rem;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .card-title {
    font-size: 1.4rem;
  }

  .card-content p {
    font-size: 1rem;
    margin-bottom: 15px;
  }

  .feature-item {
    padding: 10px;
    margin-bottom: 10px;
  }

  .developer-info {
    padding: 15px;
    margin-bottom: 20px;
  }

  .developer-name {
    font-size: 1.3rem;
    filter: drop-shadow(0 0 6px rgba(168, 85, 247, 0.5));
  }

  .contact-method {
    padding: 15px;
    flex-direction: row;
    text-align: left;
  }

  .method-icon {
    width: 45px;
    height: 45px;
    margin-right: 12px;
    margin-bottom: 0;
    font-size: 1.3rem;
  }

  .method-icon i {
    font-size: 1.3rem !important;
  }

  .method-info {
    align-items: center;
  }

  .method-value {
    font-size: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-item {
    padding: 20px 15px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 1rem;
  }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
  .new-about-container {
    padding: 75px 10px 15px;
  }

  .about-hero-section {
    margin-bottom: 30px;
    padding: 15px 0;
  }

  .about-main-title {
    font-size: clamp(1.8rem, 12vw, 2.5rem);
  }

  .about-content-grid {
    gap: 15px;
    margin-bottom: 30px;
  }

  .about-card {
    padding: 15px;
    border-radius: 15px;
  }

  .card-icon {
    font-size: 1.8rem;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-content p {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .developer-name {
    font-size: 1.2rem;
    filter: drop-shadow(0 0 5px rgba(168, 85, 247, 0.6));
  }

  .developer-role {
    font-size: 0.9rem;
  }

  .method-icon {
    width: 35px;
    height: 35px;
    font-size: 1.1rem;
  }

  .method-label {
    font-size: 0.8rem;
  }

  .method-value {
    font-size: 0.9rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }
}

/* Animation Enhancements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.about-card {
  animation: fadeInUp 0.6s ease-out;
}

.about-card:nth-child(1) {
  animation-delay: 0.1s;
}

.about-card:nth-child(2) {
  animation-delay: 0.2s;
}

.about-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stat-item {
  animation: fadeInUp 0.6s ease-out;
}

.stat-item:nth-child(1) {
  animation-delay: 0.4s;
}

.stat-item:nth-child(2) {
  animation-delay: 0.5s;
}

.stat-item:nth-child(3) {
  animation-delay: 0.6s;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .about-card,
  .stat-item,
  .contact-method,
  .feature-item {
    animation: none;
    transition: none;
  }

  .about-card:hover,
  .stat-item:hover,
  .contact-method:hover,
  .feature-item:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .about-card {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffffff;
  }

  .contact-method {
    border: 1px solid #ffffff;
  }

  .stat-item {
    border: 2px solid #ffffff;
  }
}
