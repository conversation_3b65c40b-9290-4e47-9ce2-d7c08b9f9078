import React from 'react';
import { useLocation } from 'react-router-dom';
import './App.css';

const Payment = ({ isAdmin }) => {
  const { state } = useLocation();
  const { cart = [], total = 0 } = state || {};
  const [isMobile, setIsMobile] = React.useState(false);
  const [paymentMethod, setPaymentMethod] = React.useState('normal'); // 'normal' or 'cod'
  const [codFormData, setCodFormData] = React.useState({
    email: '',
    phone: '',
    government: '',
    city: '',
  });
  const [formErrors, setFormErrors] = React.useState({});

  // Tunisia governorates and major cities
  const tunisianGovernorates = [
    'Tunis', 'Ariana', 'Ben Arous', 'Manouba', 'Nabeul', 'Zaghouan', 'Bizerte',
    'Béja', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Tataouine'
  ];

  const tunisianCities = {
    'Tunis': ['Tunis', 'La Marsa', 'Sidi Bou Said', 'Carthage', 'Le Bardo'],
    'Ariana': ['Ariana', 'Raoued', 'Soukra', 'Mnihla'],
    'Ben Arous': ['Ben Arous', 'Hammam Lif', 'Radès', 'Ezzahra'],
    'Manouba': ['Manouba', 'Denden', 'Oued Ellil', 'Douar Hicher'],
    'Nabeul': ['Nabeul', 'Hammamet', 'Kelibia', 'Korba', 'Menzel Bouzelfa'],
    'Sousse': ['Sousse', 'Msaken', 'Kalaa Kebira', 'Enfidha'],
    'Sfax': ['Sfax', 'Sakiet Ezzit', 'Sakiet Eddaier', 'Thyna'],
    'Monastir': ['Monastir', 'Ksar Hellal', 'Moknine', 'Jemmal'],
    'Bizerte': ['Bizerte', 'Menzel Bourguiba', 'Mateur', 'Ras Jebel'],
    'Gabès': ['Gabès', 'Mareth', 'El Hamma', 'Matmata'],
    'Kairouan': ['Kairouan', 'Sbikha', 'Haffouz', 'Alaa'],
    'Gafsa': ['Gafsa', 'Metlaoui', 'Redeyef', 'Moulares'],
    'Mahdia': ['Mahdia', 'Ksour Essef', 'Chebba', 'Melloulèche'],
    'Kasserine': ['Kasserine', 'Sbeitla', 'Feriana', 'Foussana'],
    'Sidi Bouzid': ['Sidi Bouzid', 'Jelma', 'Cebbala Ouled Asker', 'Bir El Hafey'],
    'Medenine': ['Medenine', 'Zarzis', 'Houmt Souk', 'Midoun'],
    'Tataouine': ['Tataouine', 'Ghomrassen', 'Bir Lahmar', 'Remada'],
    'Tozeur': ['Tozeur', 'Nefta', 'Degache', 'Hazoua'],
    'Kebili': ['Kebili', 'Douz', 'Souk Lahad', 'Faouar'],
    'Béja': ['Béja', 'Medjez el Bab', 'Testour', 'Nefza'],
    'Jendouba': ['Jendouba', 'Tabarka', 'Ain Draham', 'Fernana'],
    'Kef': ['Le Kef', 'Dahmani', 'Sers', 'Tajerouine'],
    'Siliana': ['Siliana', 'Maktar', 'Rouhia', 'Gaafour'],
    'Zaghouan': ['Zaghouan', 'Zriba', 'Bir Mcherga', 'Nadhour']
  };

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateTunisianPhone = (phone) => {
    const phoneRegex = /^\+216[0-9]{8}$/;
    return phoneRegex.test(phone);
  };

  const validateCodForm = () => {
    const errors = {};

    if (!codFormData.email) {
      errors.email = 'Email is required';
    } else if (!validateEmail(codFormData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!codFormData.phone) {
      errors.phone = 'Phone number is required';
    } else if (!validateTunisianPhone(codFormData.phone)) {
      errors.phone = 'Phone must be in format: +216 followed by 8 digits';
    }

    if (!codFormData.government) {
      errors.government = 'Government/State is required';
    }

    if (!codFormData.city) {
      errors.city = 'City is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePayment = () => {
    if (cart.length === 0) return;

    // Validate cash-on-delivery form if COD is selected
    if (paymentMethod === 'cod' && !validateCodForm()) {
      return;
    }

    // Simulate payment and log to localStorage
    const paymentData = {
      id: Date.now(), // Unique ID based on timestamp
      user: localStorage.getItem('username') || 'Guest',
      email: paymentMethod === 'cod' ? codFormData.email : (localStorage.getItem('email') || '<EMAIL>'),
      items: cart.map(item => `${item.name} (x${item.quantity})`).join(', '),
      total: parseFloat(total),
      date: new Date().toISOString().split('T')[0],
      paymentMethod: paymentMethod === 'cod' ? 'Cash on Delivery' : 'Card Payment',
      ...(paymentMethod === 'cod' && {
        phone: codFormData.phone,
        government: codFormData.government,
        city: codFormData.city
      })
    };

    // Retrieve existing payment history or initialize empty array
    const paymentHistory = JSON.parse(localStorage.getItem('paymentHistory')) || [];
    paymentHistory.push(paymentData);
    localStorage.setItem('paymentHistory', JSON.stringify(paymentHistory));

    const paymentMessage = paymentMethod === 'cod'
      ? `Cash on Delivery order placed! Total: $${total} for ${cart.length} items. We'll contact you at ${codFormData.phone} for delivery confirmation.`
      : `Processing payment of $${total} for ${cart.length} items. Payment recorded!`;

    alert(paymentMessage);
  };

  const handleCodFormChange = (field, value) => {
    setCodFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handlePhoneInput = (e) => {
    let value = e.target.value;

    // Remove all non-digits except +
    value = value.replace(/[^\d+]/g, '');

    // Ensure it starts with +216
    if (!value.startsWith('+216')) {
      if (value.startsWith('216')) {
        value = '+' + value;
      } else if (value.startsWith('+')) {
        value = '+216';
      } else if (value.length > 0) {
        value = '+216' + value;
      } else {
        value = '+216';
      }
    }

    // Limit to +216 + 8 digits
    if (value.length > 12) {
      value = value.substring(0, 12);
    }

    handleCodFormChange('phone', value);
  };

  return (
    <div className="payment-container">
      <div className="payment-header">
        <h1>🛒 Secure Checkout</h1>
        <p>Review your items and complete your purchase</p>
        <div className="security-badge">
          <span>🔒 SSL Secured</span>
        </div>
      </div>
      
      {cart.length === 0 ? (
        <p style={{ textDecoration: 'underline', color: '#fff', textShadow: '2px 2px 5px rgba(0, 0, 0, 0.7)' }}>
          No items to checkout
        </p>
      ) : (
        <>
          {/* Payment Method Toggle */}
          <div className="payment-method-toggle">
            <div className="toggle-container">
              <button
                type="button"
                className={`toggle-option ${paymentMethod === 'normal' ? 'active' : ''}`}
                onClick={() => setPaymentMethod('normal')}
              >
                💳 Card Payment
              </button>
              <button
                type="button"
                className={`toggle-option ${paymentMethod === 'cod' ? 'active' : ''}`}
                onClick={() => setPaymentMethod('cod')}
              >
                🏠 Cash on Delivery
              </button>
            </div>
          </div>

          <div className="price-breakdown">
            {cart.map(item => {
              const itemId = item._id || item.id;
              const itemImage = item.image || '/uploads/iPhone-16-Pro-& pro maxlarge.png';
              const itemPrice = typeof item.price === 'string' ? parseFloat(item.price.replace('$', '')) : item.price;
              const totalItemPrice = (itemPrice * item.quantity).toFixed(2);

              return (
                <div key={itemId} className="checkout-item">
                  <img
                    src={itemImage}
                    alt={item.name}
                    className="checkout-item-image"
                    onError={(e) => {
                      e.target.src = '/uploads/iPhone-16-Pro-& pro maxlarge.png';
                    }}
                  />
                  <div className="checkout-item-details">
                    <div className="checkout-item-name">{item.name}</div>
                    <div className="checkout-item-quantity">Quantity: {item.quantity}</div>
                    <div className="checkout-item-price">${totalItemPrice}</div>
                  </div>
                </div>
              );
            })}
            <div className="price-row total">
              <span>Total</span>
              <span className="total-price">${total}</span>
            </div>
          </div>
          {isAdmin ? (
            <p style={{ color: '#fff', textAlign: 'center' }}>
              Admins cannot make purchases. View payment history in Payment History.
            </p>
          ) : (
            <>
              {paymentMethod === 'normal' ? (
                <form className="payment-form" onSubmit={(e) => { e.preventDefault(); handlePayment(); }}>
              <div className="form-group">
                <label htmlFor="cardNumber">💳 Card Number</label>
                <input
                  type="text"
                  id="cardNumber"
                  name="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  maxLength="19"
                  required
                  onInput={(e) => {
                    // Format card number with spaces
                    let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
                    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                    e.target.value = formattedValue;
                  }}
                />
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="expiryDate">📅 Expiration Date</label>
                  <input
                    type="text"
                    id="expiryDate"
                    name="expiryDate"
                    placeholder="MM/YY"
                    maxLength="5"
                    required
                    onInput={(e) => {
                      // Format expiry date with slash
                      let value = e.target.value.replace(/\D/g, '');
                      if (value.length >= 2) {
                        value = value.substring(0, 2) + '/' + value.substring(2, 4);
                      }
                      e.target.value = value;
                    }}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="cvv">🔒 CVV</label>
                  <input
                    type="text"
                    id="cvv"
                    name="cvv"
                    placeholder="123"
                    maxLength="3"
                    required
                    onInput={(e) => {
                      // Only allow numbers
                      e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    }}
                  />
                </div>
              </div>
                  <button type="submit" className="payment-button">
                    💰 Complete Payment - ${total}
                  </button>
                </form>
              ) : (
                <form className="payment-form cod-form" onSubmit={(e) => { e.preventDefault(); handlePayment(); }}>
                  <div className="form-group">
                    <label htmlFor="codEmail">📧 Email Address</label>
                    <input
                      type="email"
                      id="codEmail"
                      name="codEmail"
                      placeholder="<EMAIL>"
                      value={codFormData.email}
                      onChange={(e) => handleCodFormChange('email', e.target.value)}
                      required
                      className={formErrors.email ? 'error' : ''}
                    />
                    {formErrors.email && <span className="error-message">{formErrors.email}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="codPhone">📱 Phone Number</label>
                    <input
                      type="tel"
                      id="codPhone"
                      name="codPhone"
                      placeholder="+216 12345678"
                      value={codFormData.phone}
                      onChange={handlePhoneInput}
                      required
                      className={formErrors.phone ? 'error' : ''}
                    />
                    {formErrors.phone && <span className="error-message">{formErrors.phone}</span>}
                    <small className="input-hint">Format: +216 followed by 8 digits</small>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="codGovernment">🏛️ Government/State</label>
                      <select
                        id="codGovernment"
                        name="codGovernment"
                        value={codFormData.government}
                        onChange={(e) => {
                          handleCodFormChange('government', e.target.value);
                          handleCodFormChange('city', ''); // Reset city when government changes
                        }}
                        required
                        className={formErrors.government ? 'error' : ''}
                      >
                        <option value="">Select Government</option>
                        {tunisianGovernorates.map(gov => (
                          <option key={gov} value={gov}>{gov}</option>
                        ))}
                      </select>
                      {formErrors.government && <span className="error-message">{formErrors.government}</span>}
                    </div>

                    <div className="form-group">
                      <label htmlFor="codCity">🏙️ City</label>
                      <select
                        id="codCity"
                        name="codCity"
                        value={codFormData.city}
                        onChange={(e) => handleCodFormChange('city', e.target.value)}
                        required
                        disabled={!codFormData.government}
                        className={formErrors.city ? 'error' : ''}
                      >
                        <option value="">Select City</option>
                        {codFormData.government && tunisianCities[codFormData.government]?.map(city => (
                          <option key={city} value={city}>{city}</option>
                        ))}
                      </select>
                      {formErrors.city && <span className="error-message">{formErrors.city}</span>}
                    </div>
                  </div>

                  <div className="cod-product-summary">
                    <h4>📦 Order Summary</h4>
                    <div className="cod-items">
                      {cart.map(item => {
                        const itemId = item._id || item.id;
                        const itemPrice = typeof item.price === 'string' ? parseFloat(item.price.replace('$', '')) : item.price;
                        return (
                          <div key={itemId} className="cod-item">
                            <span className="item-name">{item.name} (x{item.quantity})</span>
                            <span className="item-price">${(itemPrice * item.quantity).toFixed(2)}</span>
                          </div>
                        );
                      })}
                      <div className="cod-total">
                        <strong>Total: ${total}</strong>
                      </div>
                    </div>
                  </div>

                  <button type="submit" className="payment-button cod-button">
                    🏠 Place Cash on Delivery Order - ${total}
                  </button>
                </form>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default Payment;