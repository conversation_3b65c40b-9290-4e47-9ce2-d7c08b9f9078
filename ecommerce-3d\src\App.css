/* Body Background */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden; /* Hide horizontal scroll bar */
  overflow-y: auto; /* Allow vertical scrolling */
  scroll-behavior: smooth; /* Smooth scrolling */
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
    url('./wallpaper.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-color: #1a1a2e;
}

/* Hide all scroll bars globally */
*::-webkit-scrollbar {
  display: none;
}

* {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

html {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

body {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

/* Ensure root element doesn't override background */
#root {
  min-height: 100vh;
  overflow-x: hidden;
  background: transparent;
}

/* Ensure all main containers are transparent to show wallpaper */
.App {
  background: transparent;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Make sure page containers don't block wallpaper */
.page-container, .main-container, .content-container {
  background: transparent !important;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Blur effect for the background */
body.blur {
  filter: blur(5px);
  transition: filter 1s ease-in-out;
}

/* Loader Styling */
/* From Uiverse.io by Nawsome */
.socket {
  width: 200px;
  height: 200px;
  position: absolute;
  left: 50%;
  margin-left: -100px;
  top: 50%;
  margin-top: -100px;
}

.hex-brick {
  background: #000000;
  width: 30px;
  height: 17px;
  position: absolute;
  top: 5px;
  animation-name: fade00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  -webkit-animation-name: fade00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
}

.h2 {
  transform: rotate(60deg);
  -webkit-transform: rotate(60deg);
}

.h3 {
  transform: rotate(-60deg);
  -webkit-transform: rotate(-60deg);
}

.gel {
  height: 30px;
  width: 30px;
  transition: all .3s;
  -webkit-transition: all .3s;
  position: absolute;
  top: 50%;
  left: 50%;
}

.center-gel {
  margin-left: -15px;
  margin-top: -15px;
  animation-name: pulse00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  -webkit-animation-name: pulse00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
}

.c1 { margin-left: -47px; margin-top: -15px; }
.c2 { margin-left: -31px; margin-top: -43px; }
.c3 { margin-left: 1px; margin-top: -43px; }
.c4 { margin-left: 17px; margin-top: -15px; }
.c5 { margin-left: -31px; margin-top: 13px; }
.c6 { margin-left: 1px; margin-top: 13px; }
.c7 { margin-left: -63px; margin-top: -43px; }
.c8 { margin-left: 33px; margin-top: -43px; }
.c9 { margin-left: -15px; margin-top: 41px; }
.c10 { margin-left: -63px; margin-top: 13px; }
.c11 { margin-left: 33px; margin-top: 13px; }
.c12 { margin-left: -15px; margin-top: -71px; }
.c13 { margin-left: -47px; margin-top: -71px; }
.c14 { margin-left: 17px; margin-top: -71px; }
.c15 { margin-left: -47px; margin-top: 41px; }
.c16 { margin-left: 17px; margin-top: 41px; }
.c17 { margin-left: -79px; margin-top: -15px; }
.c18 { margin-left: 49px; margin-top: -15px; }
.c19 { margin-left: -63px; margin-top: -99px; }
.c20 { margin-left: 33px; margin-top: -99px; }
.c21 { margin-left: 1px; margin-top: -99px; }
.c22 { margin-left: -31px; margin-top: -99px; }
.c23 { margin-left: -63px; margin-top: 69px; }
.c24 { margin-left: 33px; margin-top: 69px; }
.c25 { margin-left: 1px; margin-top: 69px; }
.c26 { margin-left: -31px; margin-top: 69px; }
.c27 { margin-left: -79px; margin-top: -15px; }
.c28 { margin-left: -95px; margin-top: -43px; }
.c29 { margin-left: -95px; margin-top: 13px; }
.c30 { margin-left: 49px; margin-top: 41px; }
.c31 { margin-left: -79px; margin-top: -71px; }
.c32 { margin-left: -111px; margin-top: -15px; }
.c33 { margin-left: 65px; margin-top: -43px; }
.c34 { margin-left: 65px; margin-top: 13px; }
.c35 { margin-left: -79px; margin-top: 41px; }
.c36 { margin-left: 49px; margin-top: -71px; }
.c37 { margin-left: 81px; margin-top: -15px; }

.r1 {
  animation-name: pulse00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .2s;
  -webkit-animation-name: pulse00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .2s;
}

.r2 {
  animation-name: pulse00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .4s;
  -webkit-animation-name: pulse00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .4s;
}

.r3 {
  animation-name: pulse00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .6s;
  -webkit-animation-name: pulse00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .6s;
}

.r1 > .hex-brick {
  animation-name: fade00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .2s;
  -webkit-animation-name: fade00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .2s;
}

.r2 > .hex-brick {
  animation-name: fade00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .4s;
  -webkit-animation-name: fade00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .4s;
}

.r3 > .hex-brick {
  animation-name: fade00;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: .6s;
  -webkit-animation-name: fade00;
  -webkit-animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-delay: .6s;
}

@keyframes pulse00 {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.01);
    transform: scale(0.01);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes fade00 {
  0% {
    background: #5504f7;
  }
  50% {
    background: white;
  }
  100% {
    background: #353535;
  }
}

/* Navbar */
.navbar {
  height: 60px;
  background-color: #0000004d;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 0 1rem;
}

.nav-center {
  display: flex;
  gap: 2rem;
  margin: 0 auto;
}

.nav-right {
  position: absolute;
  right: 2rem;
  display: flex;
  gap: 1rem;
}

/* Remove borders, circles, and underline */
.nav-button {
  border: none;
  outline: none;
  background: none;
  color: white;
  padding: 12px 30px;
  font-size: 18px;
  cursor: pointer;
  transition: 0.3s ease;
  text-decoration: none;
  font-family: inherit;
}

.nav-button:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ffcc;
}

.nav-button:focus {
  outline: none;
  box-shadow: none;
}

.nav-link {
  border: 2px solid #00ffcc;
  color: #00ffcc;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 18px;
  background: transparent;
  cursor: pointer;
  transition: 0.3s ease;
}

.nav-link:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ffcc;
}

@media (max-width: 768px) {
  .navbar {
    padding: .5rem 1rem;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    flex-wrap: nowrap;
    position: relative;
  }

  /* MOBILE - MAXIMUM SPECIFICITY FOR FIXED POSITION */
  html body div.pageTitle,
  html body .pageTitle,
  body div.pageTitle,
  body .pageTitle,
  div.pageTitle,
  .pageTitle {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 99999 !important;
    width: auto !important;
    order: 1;
    margin: 0 !important;
    padding: 10px 20px !important;
    font-size: 18px !important;
    background: transparent !important;
    text-align: center !important;
    color: white !important;
    text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
    display: block !important;
    visibility: visible !important;
  }

  .mobile-nav-toggle {
    display: flex;
    order: 3;
    margin-left: auto;
    position: relative;
  }

  .nav-center, .nav-right {
    display: none;
  }

  .mobile-nav {
    width: 250px;
  }

  .mobile-nav .nav-link {
    padding: 15px 20px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: .5rem .75rem;
  }

  /* SMALL MOBILE - MAXIMUM SPECIFICITY FOR FIXED POSITION */
  html body div.pageTitle,
  html body .pageTitle,
  body div.pageTitle,
  body .pageTitle,
  div.pageTitle,
  .pageTitle {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 99999 !important;
    font-size: 16px !important;
    padding: 8px 16px !important;
    background: transparent !important;
    color: white !important;
    text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
    display: block !important;
    visibility: visible !important;
  }

  .mobile-nav {
    width: 220px;
  }

  .mobile-nav .nav-link {
    padding: 12px 20px;
    font-size: 14px;
  }
}

/* Navigation list */
.navList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Left-side (centered items) */
.navCenter {
  display: flex;
  justify-content: center;
  flex: 0;
}

/* Right-side (login & register) */
.navRight {
  display: flex;
  margin-left: auto;
}

/* Navigation items */
.navItem {
  list-style: none;
  margin: 0 10px;
}

/* Unified button styles for all nav buttons */
.nav-link {
  border: none;
  color: #00ffcc;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 18px;
  background: transparent;
  cursor: pointer;
  transition: 0.3s ease;
  text-align: center;
  display: flex;
  align-items: center;
  text-decoration: none;
}

/* Hover effect for all nav buttons */
.nav-link:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 10px #00ccff, 0 0 40px #00ccff;
}

/* Fix nav-link styling conflicts in modals */
.modal-overlay .nav-link,
.product-modal .nav-link {
  border: 2px solid #00ffcc;
  background: rgba(0, 255, 204, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 255, 204, 0.2);
  min-width: 120px;
  justify-content: center;
  font-weight: 500;
}

.modal-overlay .nav-link:hover,
.product-modal .nav-link:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 15px #00ccff, 0 0 30px #00ccff;
  transform: translateY(-2px);
}

/* Ensure modal buttons are properly positioned */
.product-modal .modal-content .nav-link {
  margin-top: 20px;
  align-self: center;
  width: fit-content;
}

.product-window .nav-link {
  margin-top: auto;
  align-self: center;
  width: fit-content;
}

/* Discover Our Product Line Section */
.discover-products-section {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.7);
  text-align: center;
}

/* PAGE TITLE - MAXIMUM SPECIFICITY TO STAY FIXED */
html body div.pageTitle,
html body .pageTitle,
body div.pageTitle,
body .pageTitle,
div.pageTitle,
.pageTitle {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 99999 !important;
  color: white !important;
  padding: 15px 30px !important;
  border-radius: 8px !important;
  font-size: 24px !important;
  font-weight: bold !important;
  text-align: center !important;
  text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
  margin: 0 !important;
  background: transparent !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  visibility: visible !important;
}

/* FORCE FIXED POSITION - OVERRIDE ALL POSSIBLE CONFLICTS */
html body div[class*="pageTitle"],
html body *[class*="pageTitle"],
html body div.pageTitle,
html body .pageTitle,
body div.pageTitle,
body .pageTitle,
div.pageTitle,
.pageTitle,
*[class="pageTitle"],
[class="pageTitle"] {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 99999 !important;
}

/* SPECIFIC OVERRIDES FOR ABOUT AND PRODUCTS PAGES */
html body div .pageTitle,
html body main .pageTitle,
html body section .pageTitle,
html body article .pageTitle,
html body * .pageTitle {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 99999 !important;
}

.payment-container.active ~ .pageTitle {
  display: none;
}

/* Floating Window */
.floating-window {
  position: fixed;
  right: -100%;
  top: 50%;
  transform: translateY(-50%);
  background: #000000b3;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  transition: right 0.3s ease-in-out;
  width: 300px;
  max-width: 90%;
  z-index: 1000;
}

.floating-window.visible {
  right: 20px;
}

.window-img {
  width: 100%;
  height: auto;
  border-radius: 10px;
  margin-bottom: 15px;
}

.window-info {
  color: white;
  text-align: center;
}

.window-name {
  font-size: 20px;
  font-weight: bold;
}

.window-price {
  font-size: 18px;
  margin: 10px 0;
  color: #45f22e;
  text-shadow: 2px 2px 5px rgba(236, 240, 7, 0.5);
}

.price {
  color: #45f22e;
  font-size: 25px;
  margin: 10px 0;
  text-shadow: 2px 2px 5px rgba(236, 240, 7, 0.5);
}

.window-description {
  font-size: 16px;
  margin-top: 5px;
  color: white;
  text-align: center;
  padding: 10px;
  border-radius: 5px;
  text-shadow: 2px 2px 5px rgba(236, 240, 7, 0.5);
}

/* Backdrop for blur effect */
.add-product-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  z-index: 999;
}

/* Add Product Window */
.add-product-window {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #ccc;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 3px 5px rgba(168, 86, 245, 0.4);
  z-index: 1000;
  width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.8);
  transition: transform 0.3s ease;
}

.add-product-window:hover {
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: 0 6px 12px rgba(255, 215, 0, 0.5);
  border-color: #45f22e;
}

/* Form content */
.add-product-window .form-content {
  color: white;
  text-align: center;
}

.add-product-window h3 {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 1px 1px 3px rgba(236, 240, 7, 0.3);
}

/* Redesigned inputs */
.add-product-window input[type="number"],
.add-product-window input[type="text"],
.add-product-window textarea {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #45f22e;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  font-size: 14px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.add-product-window input[type="number"]:focus,
.add-product-window input[type="text"]:focus,
.add-product-window textarea:focus {
  outline: none;
  border-color: gold;
  box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

.add-product-window textarea {
  height: 100px;
  resize: vertical;
}

/* File input styling */
.add-product-window .file-input-wrapper {
  position: relative;
  width: 100%;
  margin: 10px 0;
}

.add-product-window input[type="file"] {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #45f22e;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  font-size: 14px;
  cursor: pointer;
  box-sizing: border-box;
}

.add-product-window input[type="file"]::-webkit-file-upload-button {
  background-color: #45f22e;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-product-window input[type="file"]::-webkit-file-upload-button:hover {
  background-color: gold;
  color: black;
}

/* Image preview */
.add-product-window .image-preview {
  width: 100%;
  max-height: 150px;
  object-fit: contain;
  margin-top: 10px;
  border-radius: 6px;
  border: 1px solid #45f22e;
}

/* Buttons */
.add-product-window button {
  margin-top: 15px;
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  background-color: #45f22e;
  color: white;
  font-size: 16px;
  cursor: pointer;
  text-shadow: 1px 1px 3px rgba(236, 240, 7, 0.3);
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.add-product-window button:hover {
  background-color: gold;
  color: black;
  transform: scale(1.05);
}

.add-product-window button.cancel {
  background-color: #555;
  margin-left: 15px;
}

.add-product-window button.cancel:hover {
  background-color: #777;
}

/* Error message */
.add-product-window p.error {
  color: red;
  font-size: 14px;
  margin: 5px 0;
}

/* Styles of the product detail after click Floating window at Home page */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Very high z-index to be above everything */
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.product-modal {
  background: #000000b3;
  box-shadow: 0 4px 6px rgba(168, 86, 245, 0.5);
  border: 1px solid #ddd;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: 20px;
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  display: flex;
  gap: 30px;
  position: relative;
  color: white;
  z-index: 10000; /* Higher than modal overlay to ensure it's on top */
  animation: slideInScale 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-sizing: border-box;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.product-modal:hover {
  box-shadow: 0 0 20px rgba(168, 86, 245, 0.7);
  border-color: gold;
}

.modal-image {
  width: 300px;
  max-width: 100%;
  height: 300px;
  object-fit: contain;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modal-content {
  color: white;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* Prevent flex item from overflowing */
}

.modal-content h2 {
  color: #ede9ca;
  text-shadow: 0 0 10px rgb(246, 223, 11);
  margin: 0 0 15px 0;
  font-size: 2rem;
  line-height: 1.2;
}

.modal-price {
  font-size: 1.5rem;
  margin: 10px 0 20px 0;
  color: #00ffcc;
  font-weight: bold;
}

.modal-description {
  color: #ccc;
  margin: 15px 0 25px 0;
  line-height: 1.6;
  font-size: 1rem;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.buy-now-btn {
  background: linear-gradient(45deg, #00c853, #00e676);
  color: white;
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  cursor: pointer;
  margin-top: 20px;
  transition: transform 0.3s ease;
}

.buy-now-btn:hover {
  transform: scale(1.05);
}

.specs-list {
  margin: 20px 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.spec-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  color: #00ffcc;
  border-left: 3px solid #00ffcc;
  transition: all 0.3s ease;
}

.spec-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

/* Comprehensive Mobile Responsive Styles for Product Modal */
@media (max-width: 768px) {
  .product-modal {
    flex-direction: column;
    width: 95%;
    max-width: none;
    padding: 20px;
    gap: 20px;
    max-height: 90vh;
    overflow-y: auto;
    margin: 20px;
    border-radius: 15px;
  }

  .modal-image {
    width: 100%;
    height: 250px;
    object-fit: contain;
    align-self: center;
  }

  .modal-content {
    text-align: center;
    gap: 15px;
  }

  .modal-content h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .modal-price {
    font-size: 1.3rem;
    margin: 10px 0 15px 0;
  }

  .specs-list {
    grid-template-columns: 1fr;
    gap: 10px;
    margin: 15px 0;
  }

  .spec-item {
    font-size: 0.9rem;
    padding: 10px;
    text-align: left;
  }

  .modal-description {
    font-size: 0.9rem;
    margin: 15px 0 20px 0;
  }

  .close-btn {
    width: 35px;
    height: 35px;
    font-size: 20px;
    top: 10px;
    right: 15px;
  }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
  .product-modal {
    width: 98%;
    padding: 15px;
    gap: 15px;
    margin: 10px;
    max-height: 95vh;
  }

  .modal-image {
    height: 200px;
  }

  .modal-content h2 {
    font-size: 1.3rem;
  }

  .modal-price {
    font-size: 1.1rem;
  }

  .spec-item {
    font-size: 0.8rem;
    padding: 8px;
  }

  .modal-description {
    font-size: 0.8rem;
  }

  .close-btn {
    width: 30px;
    height: 30px;
    font-size: 18px;
    top: 8px;
    right: 12px;
  }
}

/* Tablet Responsive Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .product-modal {
    width: 85%;
    max-width: 700px;
    padding: 25px;
    gap: 25px;
  }

  .modal-image {
    width: 250px;
    height: 250px;
  }

  .modal-content h2 {
    font-size: 1.8rem;
  }

  .modal-price {
    font-size: 1.4rem;
  }

  .specs-list {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

/* Footer */
footer {
  height: 50px;
  background-color: #0000004d;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  color: white;
  text-align: center;
  padding: 0 1rem;
}

/* Mobile page title - show only on home page in mobile */
.mobile-page-title {
  display: none !important;
}

/* Show mobile page title only on home page */
@media (max-width: 768px) {
  .mobile-page-title.show-on-home {
    display: block !important;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: bold !important;
    text-align: center !important;
    text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
    white-space: nowrap !important;
    max-width: 200px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    z-index: 1001 !important;
  }
}

@media (max-width: 480px) {
  .mobile-page-title.show-on-home {
    font-size: 14px !important;
    max-width: 180px !important;
  }
}

/* Hide page title on mobile for home page since it's shown in navbar */
@media (max-width: 768px) {
  .pageTitle.hide-on-mobile-home {
    display: none !important;
  }
}

/* Force hide pageTitle on About and Products pages - MAXIMUM SPECIFICITY */
html body div.pageTitle.hide-page-title,
html body .pageTitle.hide-page-title,
body div.pageTitle.hide-page-title,
body .pageTitle.hide-page-title,
div.pageTitle.hide-page-title,
.pageTitle.hide-page-title,
*[class*="pageTitle"].hide-page-title {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  z-index: -1 !important;
}

/* Ensure mobile page title is hidden on About page specifically */
.new-about-container .mobile-page-title,
.about-container .mobile-page-title {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  left: -9999px !important;
}

/* Username in Footer */
.username-footer {
  color: #00ffcc;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 5px #00ffcc, 0 0 10px #00ffcc;
  padding: 5px 10px;
  border-radius: 15px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #00ffcc;
}

@media (max-width: 768px) {
  footer {
    padding: .5rem 1rem;
    justify-content: space-between;
  }

  .username-footer {
    font-size: 14px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  footer {
    padding: .5rem .75rem;
  }

  .username-footer {
    font-size: 12px;
    padding: 3px 6px;
  }
}

/* Floating Window Toggle Button */
.floating-window-toggle {
  display: none;
  border: 2px solid #00ffcc;
  color: #00ffcc;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  background: transparent;
  cursor: pointer;
  transition: 0.3s ease;
}

.floating-window-toggle:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ffcc;
}

/* Footer Paragraph */
footer p {
  margin: 0;
  flex: 1;
  text-align: center;
}

/* Cart Link and Badge */
.cart-link {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.cart-badge {
  position: static;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  width: 20px;
  height: 20px;
  min-width: 20px;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .cart-badge {
    font-size: 10px;
    width: 18px;
    height: 18px;
    min-width: 18px;
    margin-left: 6px;
  }
}

/* Cart Container - Redesigned to match payment-container */
.cart-container {
  position: relative;
  max-width: 800px;
  margin: 100px auto 80px;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: auto;
  max-height: 90vh;
  overflow-y: auto;
}

.cart-header {
  text-align: center;
  margin-bottom: 1rem;
}

.cart-header h1 {
  font-size: 2rem;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cart-header p {
  font-size: 0.9rem;
  color: #ccc;
  margin: 0;
}

.cart-items {
  margin-bottom: 0;
  flex: 1;
  overflow-y: auto;
  max-height: 70vh;
}

.cart-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cart-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cart-item-image {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-right: 1rem;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.cart-item-details {
  flex: 1;
  color: white;
}

.cart-item-details h3 {
  margin: 0 0 0.5rem 0;
  font-size: 18px;
  color: #ffffff;
}

.cart-item-details .cart-item-price {
  margin: 0 0 0.5rem 0;
  color: #45f22e;
  font-weight: bold;
  font-size: 16px;
}

.cart-item-details .cart-item-description {
  margin: 0 0 0.5rem 0;
  color: #cccccc;
  font-size: 14px;
  line-height: 1.4;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.quantity-controls button {
  padding: 0.25rem 0.75rem;
  border: 1px solid #00ffcc;
  background: rgba(0, 0, 0, 0.7);
  color: #00ffcc;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.quantity-controls button:hover {
  background: #00ffcc;
  color: black;
}

.quantity-controls span {
  min-width: 20px;
  text-align: center;
}

.remove-item {
  background: #dc3545;
  color: white;
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.remove-item:hover {
  background: #c82333;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #ff0000;
  transform: translateY(-2px);
}

.cart-summary {
  padding: 1rem;
  text-align: center;
  color: white;
  margin-top: 0;
  flex-shrink: 0;
  background: transparent;
  border: none;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.cart-summary h3 {
  margin: 0;
  color: #45f22e;
  font-size: 1.3rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cart-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.clear-cart {
  background: #dc3545;
  color: white;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  flex: 1;
  max-width: 45%;
}

.checkout-button {
  display: inline-block !important;
  padding: 8px 12px !important;
  background: #45f22e !important;
  color: white !important;
  text-decoration: none !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  font-weight: 600 !important;
  border: 2px solid #45f22e !important;
  min-width: 180px !important;
  text-align: center !important;
  cursor: pointer !important;
  box-sizing: border-box !important;
  vertical-align: middle !important;
}

.checkout-button:hover {
  background: #00ff2a !important;
  color: black !important;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ff2a !important;
  transform: translateY(-2px) !important;
}

/* Cart Container Responsive Design */
@media (max-width: 768px) {
  .cart-container {
    max-width: 95%;
    margin: 80px auto 2rem;
    padding: 1.5rem;
    max-height: 85vh;
    gap: 0.8rem;
  }

  .cart-items {
    max-height: 65vh;
    margin-bottom: 0;
  }

  .cart-summary {
    padding: 0.8rem;
    margin-top: 0;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .cart-container {
    margin: 60px auto 1rem;
    padding: 1rem;
    max-height: 80vh;
    gap: 0.5rem;
  }

  .cart-items {
    max-height: 60vh;
    margin-bottom: 0;
  }

  .cart-summary {
    padding: 0.6rem;
    margin-top: 0;
    gap: 0.3rem;
  }
}

/* Button Styles */
button {
  border: 2px solid #00ffcc;
  color: #00ffcc;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 18px;
  background: transparent;
  cursor: pointer;
  transition: 0.3s ease;
}

button:hover {
  background: #00ffcc;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ffcc;
}

/* General Page Layout */
.content {
  text-align: center;
}

/* Fixed title for Products page */
.products-page-title {
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  color: white !important;
  padding: 15px 0 !important;
  font-size: 24px !important;
  font-weight: bold !important;
  text-align: center !important;
  text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
  margin: 0 !important;
  margin-top: 60px !important; /* Add top margin to account for fixed navbar in PC mode */
  background: transparent !important;
}

/* Products Container */
.products-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 20px auto 80px;
  box-sizing: border-box;
  justify-content: center;
}

@media (max-width: 768px) {
  .products-page-title {
    font-size: 20px !important;
    padding: 12px 0 !important;
    margin-top: 0 !important; /* Remove top margin in mobile mode */
  }
}

@media (max-width: 480px) {
  .products-page-title {
    font-size: 18px !important;
    padding: 10px 0 !important;
    margin-top: 0 !important; /* Remove top margin in small mobile mode */
  }
}

.product-box {
  width: 100%;
  height: 240px;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  box-shadow: 0px 4px 15px rgba(255, 105, 180, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  padding: 15px;
  box-sizing: border-box;
  text-decoration: none;
  will-change: transform; /* Performance optimization */
}

.product-box:hover {
  transform: scale(1.08); /* Reduced scale for smoother animation */
  box-shadow: 0px 8px 16px rgba(255, 215, 0, 0.8);
  border-color: gold;
}

/* Disable hover effects on touch devices for better performance */
@media (hover: none) and (pointer: coarse) {
  .product-box:hover {
    transform: none;
    box-shadow: 0px 4px 15px rgba(255, 105, 180, 0.5);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

.product-link {
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.product-image {
  width: 100%;
  height: 120px;
  object-fit: contain;
  border-radius: 8px;
  margin-bottom: 10px;
}

.product-name {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: white;
  margin-top: 10px;
}

/* Responsive Styles for Products Container */
@media screen and (max-width: 1200px) {
  .products-container {
    grid-template-columns: repeat(3, 1fr);
    max-width: 900px;
    gap: 15px;
    padding: 15px;
  }
}

@media screen and (max-width: 900px) {
  .products-container {
    grid-template-columns: repeat(2, 1fr);
    max-width: 600px;
    gap: 15px;
    padding: 15px;
  }
}

@media screen and (max-width: 600px) {
  .products-container {
    grid-template-columns: repeat(2, 1fr); /* Maintain 2x2 grid */
    max-width: 100%;
    gap: 12px;
    padding: 12px;
    margin: 70px auto 80px; /* Better spacing */
  }

  .product-box {
    height: 200px; /* Optimized height for mobile */
    padding: 12px;
  }

  .product-image {
    height: 90px; /* Optimized image size */
  }

  .product-name {
    font-size: 12px;
    margin-top: 8px;
  }
}

@media screen and (max-width: 400px) {
  .products-container {
    grid-template-columns: repeat(2, 1fr); /* Keep 2x2 grid on very small screens */
    gap: 8px;
    padding: 8px;
    margin-top: 70px;
  }

  .product-box {
    height: 180px; /* Slightly smaller for very small screens */
    padding: 10px;
  }

  .product-image {
    height: 80px;
  }

  .product-name {
    font-size: 12px;
  }
}

/* About container */
.about-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0 50px;
}

/* Mobile responsive styles for About page */
@media (max-width: 768px) {
  .about-container.mobile {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    height: 100vh;
    overflow-y: auto;
    gap: 40px;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .about-container.tablet {
    padding: 0 40px;
    gap: 30px;
  }
}

/* About window (left side) */
.about-window {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  padding: 30px;
  width: 60%;
  max-width: 600px;
  border-radius: 15px;
  border: 1px solid #ddd;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  text-align: left;
  backdrop-filter: blur(10px);
}

/* Mobile responsive styles for about-window */
@media (max-width: 768px) {
  .about-container.mobile .about-window {
    width: 100%;
    max-width: none;
    padding: 25px;
    margin-bottom: 0;
    text-align: left;
  }

  .about-container.mobile .about-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
  }

  .about-container.mobile .about-description {
    font-size: 1.1rem;
    line-height: 1.6;
    padding: 18px;
    margin-bottom: 18px;
  }
}

/* Title styling */
.about-title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15px;
}

/* Description text styling */
.about-description {
  font-size: 1.2rem;
  text-shadow: rgb(118, 30, 224) 2px 2px 5px;
  line-height: 1.6;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.7);
  margin-bottom: 15px;
}

/* Contact window (right side) */
.contact-window {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 20px;
  width: 30%;
  max-width: 300px;
  border-radius: 15px;
  border: 1px solid #ddd;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  text-align: center;
  backdrop-filter: blur(10px);
  animation: slideIn 0.5s ease-in-out;
}

/* Mobile responsive styles for contact-window */
@media (max-width: 768px) {
  .about-container.mobile .contact-window {
    width: 100%;
    max-width: none;
    padding: 25px;
    margin-top: 0;
  }

  .about-container.mobile .social-links {
    gap: 20px;
  }

  .about-container.mobile .social-link {
    width: 40px;
    height: 40px;
    font-size: 1.3rem;
    margin: 0 auto;
  }
}

/* Contact name */
.contact-name {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
  text-shadow: 0 0 5px rgba(118, 30, 224, 0.8);
}

/* Contact info */
.contact-info {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: #ddd;
}

/* Social links container */
.social-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Social link styling */
.social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  text-decoration: none;
  color: #fff;
  padding: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover effects for social links */
.social-link:hover {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(91, 3, 244, 0.7);
}

/* Specific link styles */
.linkedin {
  background-color: rgba(0, 119, 181, 0.7);
}

.instagram {
  background-color: rgba(225, 48, 108, 0.7);
}

.email {
  background-color: rgba(91, 3, 244, 0.7);
}

/* Animation for contact window */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Product Details Page */
.product-details-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Background element */
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  transition: backdrop-filter 1s ease-in-out;
}

.background.blur {
  backdrop-filter: blur(5px);
}

/* Product Details Window */
.product-window {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  text-align: center;
  z-index: 10;
  height: 500px;
  width: 200px;
  max-width: 90%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Hide product window when modal is open - HIGHEST PRIORITY */
.product-window.move-right.hidden {
  opacity: 0 !important;
  pointer-events: none !important;
  transform: translate(-50%, -50%) scale(0.8) !important;
  z-index: 5 !important;
  visibility: hidden !important;
  left: 50% !important; /* Override mobile positioning */
  top: 50% !important; /* Override mobile positioning */
}



.product-window:hover {
  transform: translate(-50%, -50%) scale(1.2);
  box-shadow: 0 0 20px rgba(255, 255, 0, 0.7);
  transition: all 0.3s ease-in-out;
}

.product-window img {
  max-width: 100%;
  max-height: 200px;
  height: auto;
  object-fit: contain;
  border-radius: 10px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.05);
  padding: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.product-window img:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.product-window h2 {
  margin: 0;
  font-size: 2rem;
  font-weight: bold;
  color: #ede9ca;
  text-shadow: 0 0 10px rgb(246, 223, 11);
  background: none;
}

.product-window p {
  margin: 10px 0 5px;
  font-size: 1.1rem;
  color: #B0BEC5;
  text-shadow: 0 0 10px rgba(176, 190, 197, 0.5);
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}

.product-window .price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #45f22e;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.product-window .close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.product-window .close-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.product-page.blur {
  filter: blur(5px);
  pointer-events: none;
}

.move-right {
  left: 88%;
  transition: left 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Mobile responsive styles for product window in ProductDetails */
@media (max-width: 768px) {
  /* Only apply mobile positioning when modal is NOT open */
  .product-details-container.mobile .product-window.move-right.hidden:not(.modal-open) {
    left: 100%;
    opacity: 0;
    pointer-events: none;
    transition: left 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  .product-details-container.mobile .product-window.move-right.visible:not(.modal-open) {
    left: 75%;
    opacity: 1;
    pointer-events: auto;
    transition: left 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  /* When modal is open, force hide regardless of mobile state */
  .product-details-container.mobile .product-window.move-right.hidden.modal-open {
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) scale(0.8) !important;
    opacity: 0 !important;
    pointer-events: none !important;
    visibility: hidden !important;
    z-index: 5 !important;
  }

  .product-details-container.mobile .product-window {
    width: 180px;
    height: 450px;
    padding: 20px;
  }

  .product-details-container.mobile .product-window h2 {
    font-size: 1.5rem;
  }

  .product-details-container.mobile .product-window p {
    font-size: 1rem;
  }

  .product-details-container.mobile .product-window img {
    max-height: 150px;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .product-details-container.tablet .product-window.move-right.hidden {
    left: 100%;
    opacity: 0;
    pointer-events: none;
    transition: left 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  .product-details-container.tablet .product-window.move-right.visible {
    left: 80%;
    opacity: 1;
    pointer-events: auto;
    transition: left 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }
}

/* Login Register */

/* Main Container - Moved to Home.css to avoid conflicts */

/* Background Elements */
.background-content {
  position: relative;
  height: 100%;
  width: 100%;
  transition: filter 0.3s ease;
}

.background-content.blurred {
  filter: blur(5px);
  pointer-events: none;
}

/* Auth Overlay */
.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Auth Modal */
.auth-modal {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  border: 1px solid rgba(255, 235, 0, 0.3);
  box-shadow: 0 4px 6px rgba(168, 86, 245, 0.5),
              0 4px 6px rgba(168, 86, 245, 0.5);
  position: relative;
  z-index: 1001;
  backdrop-filter: blur(5px);
}

.auth-modal:hover {
  box-shadow: 0 0 40px rgba(255, 235, 0, 0.5), 
              0 0 60px rgb(255, 235, 0, 0.3);
}

/* Title Styling */
.auth-modal h2 {
  color: #fff !important;
  text-align: center;
  margin: 0 0 2rem 0;
  padding: 0;
  font-size: 2rem;
  text-shadow: 0 0 10px rgba(255, 235, 0, 0.7);
  background: transparent !important;
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

/* Form Elements */
.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  color: #fff;
  font-size: 1rem;
  text-shadow: 0 0 8px rgba(255, 235, 0, 0.5);
}

.form-group input {
  width: 91%;
  padding: 1rem;
  border: 2px solid rgba(255, 235, 0, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(255, 235, 0, 0.2);
}

.form-group input:focus {
  outline: none;
  border-color: #ffeb3b;
  box-shadow: 0 0 20px rgba(255, 235, 0, 0.5);
}

/* Close Button */
.close-auth {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #ffffff !important;
  font-size: 28px;
  cursor: pointer;
  text-shadow: none;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 1002;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
}

.close-auth:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* Submit Button */
.auth-submit {
  width: auto;
  padding: 1rem 2rem;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
  display: block;
  margin: 1rem auto;
}

.auth-submit:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(76, 175, 80, 0.7);
}

/* Switch Button */
.auth-switch {
  text-align: right;
}

.switch-btn {
  color: #4CAF50 !important;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  margin-left: auto;
  width: fit-content;
}

.switch-btn:hover {
  text-shadow: 0 0 15px rgba(76, 175, 80, 0.7);
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Input Placeholder Styling */
.form-group input::placeholder {
  color: #666;
  opacity: 1;
}

/* Error Message Styling (if needed later) */
.error-message {
  color: #ff4444;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  text-shadow: 0 0 8px rgba(255, 68, 68, 0.3);
}

/* Payment section */
.payment-container {
  position: relative;
  max-width: 450px;
  margin: 100px auto 80px;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (max-width: 768px) {
  .payment-container {
    max-width: 95%;
    margin: 120px auto 2rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .payment-container {
    margin: 100px auto 1rem;
    padding: 0.8rem;
  }
}

.payment-header {
  text-align: center;
}

.payment-header h1 {
  font-size: 1.6rem;
  color: #fff;
  margin-bottom: 0.4rem;
}

.payment-header p {
  font-size: 0.9rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.security-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.4);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  color: #4caf50;
  margin-top: 0.5rem;
}

.security-badge span {
  font-weight: 600;
}

/* Payment Form Redesign */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 400px;
}

.payment-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  position: relative;
}

.payment-form .form-group label {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.payment-form .form-group input {
  padding: 16px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-sizing: border-box;
}

.payment-form .form-group input:focus {
  border-color: #00ffcc;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 255, 204, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.payment-form .form-group input::placeholder {
  color: #888;
  font-style: italic;
}

.payment-form .form-group input:hover {
  border-color: rgba(0, 255, 204, 0.4);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Input validation states */
.payment-form .form-group input:valid {
  border-color: rgba(76, 175, 80, 0.5);
}

.payment-form .form-group input:invalid:not(:placeholder-shown) {
  border-color: rgba(244, 67, 54, 0.5);
}

/* Add subtle animation to form groups */
.payment-form .form-group {
  animation: slideInUp 0.6s ease-out;
}

.payment-form .form-group:nth-child(1) {
  animation-delay: 0.1s;
}

.payment-form .form-group:nth-child(2) {
  animation-delay: 0.2s;
}

.payment-button {
  animation: slideInUp 0.6s ease-out;
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.payment-form .form-row {
  display: flex;
  gap: 1rem;
}

.payment-form .form-row .form-group {
  flex: 1;
}

.price-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  width: 100%;
}

.price-row {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #eee;
}

.price-row.total {
  border-bottom: none;
  font-weight: bold;
  font-size: 1rem;
}

.total-price {
  color: red;
}

/* Payment Button - Using checkout-button style */
.payment-button {
  display: inline-block;
  padding: 18px 32px;
  background: #45f22e;
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  width: 100%;
}

.payment-button:hover {
  background: #00ff2a;
  color: black;
  box-shadow: 0 0 10px #00ffcc, 0 0 40px #00ff2a;
  transform: translateY(-2px);
}

/* Payment Form Responsive Design */
@media (max-width: 768px) {
  .payment-form {
    gap: 1.2rem;
  }

  .payment-form .form-group input {
    padding: 14px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .payment-form .form-row {
    gap: 0.8rem;
  }

  .payment-button {
    padding: 16px 28px;
    font-size: 15px;
    transform: none;
  }

  .payment-button:hover {
    transform: translateY(-1px);
  }
}

@media (max-width: 480px) {
  .payment-form .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .payment-form .form-group input {
    padding: 12px 14px;
  }

  .payment-button {
    padding: 14px 24px;
    font-size: 14px;
  }
}

/* Payment Method Toggle */
.payment-method-toggle {
  width: 100%;
  margin-bottom: 1.5rem;
}

.toggle-container {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-option {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #ccc;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.toggle-option.active {
  background: #00ffcc;
  color: #000;
  box-shadow: 0 2px 8px rgba(0, 255, 204, 0.3);
}

.toggle-option:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Cash on Delivery Form Styles */
.cod-form .form-group select {
  padding: 16px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-sizing: border-box;
  cursor: pointer;
}

.cod-form .form-group select:focus {
  border-color: #00ffcc;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 255, 204, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.cod-form .form-group select:disabled {
  background: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.7;
}

.error-message {
  color: #ff4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}

.input-hint {
  color: #aaa;
  font-size: 11px;
  margin-top: 4px;
  display: block;
  font-style: italic;
}

.payment-form .form-group input.error,
.payment-form .form-group select.error {
  border-color: #ff4444;
  box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.2);
}

/* COD Product Summary */
.cod-product-summary {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cod-product-summary h4 {
  color: #00ffcc;
  margin: 0 0 0.8rem 0;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cod-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cod-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cod-item:last-child {
  border-bottom: none;
}

.item-name {
  color: #fff;
  font-size: 14px;
}

.item-price {
  color: #00ffcc;
  font-weight: 600;
}

.cod-total {
  margin-top: 0.8rem;
  padding-top: 0.8rem;
  border-top: 2px solid rgba(0, 255, 204, 0.3);
  text-align: right;
  color: #00ffcc;
  font-size: 18px;
}

.cod-button {
  background: #ff6b35 !important;
}

.cod-button:hover {
  background: #ff8c42 !important;
  box-shadow: 0 0 10px #ff6b35, 0 0 40px #ff6b35 !important;
}

/* Mobile Responsive Styles for Payment Toggle and COD */
@media (max-width: 768px) {
  .toggle-option {
    padding: 10px 12px;
    font-size: 12px;
  }

  .cod-form .form-group select {
    padding: 14px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .cod-product-summary {
    padding: 0.8rem;
    margin: 0.8rem 0;
  }

  .cod-product-summary h4 {
    font-size: 14px;
  }

  .cod-item {
    padding: 0.4rem 0;
  }

  .item-name {
    font-size: 13px;
  }

  .cod-total {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .toggle-container {
    flex-direction: column;
    gap: 2px;
  }

  .toggle-option {
    padding: 12px;
    font-size: 13px;
  }

  .cod-form .form-group select {
    padding: 12px 14px;
  }

  .error-message {
    font-size: 11px;
  }

  .input-hint {
    font-size: 10px;
  }

  .cod-product-summary {
    padding: 0.6rem;
  }

  .cod-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.2rem;
  }

  .item-price {
    align-self: flex-end;
  }
}

/* Payment Method Toggle */
.payment-method-toggle {
  width: 100%;
  margin-bottom: 1.5rem;
}

.toggle-container {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-option {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #ccc;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.toggle-option.active {
  background: #00ffcc;
  color: #000;
  box-shadow: 0 2px 8px rgba(0, 255, 204, 0.3);
}

.toggle-option:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Cash on Delivery Form Styles */
.cod-form .form-group select {
  padding: 16px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-sizing: border-box;
  cursor: pointer;
}

.cod-form .form-group select:focus {
  border-color: #00ffcc;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 255, 204, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.cod-form .form-group select:disabled {
  background: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.7;
}

.error-message {
  color: #ff4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}

.input-hint {
  color: #aaa;
  font-size: 11px;
  margin-top: 4px;
  display: block;
  font-style: italic;
}

.payment-form .form-group input.error,
.payment-form .form-group select.error {
  border-color: #ff4444;
  box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.2);
}

/* COD Product Summary */
.cod-product-summary {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cod-product-summary h4 {
  color: #00ffcc;
  margin: 0 0 0.8rem 0;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cod-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cod-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cod-item:last-child {
  border-bottom: none;
}

.item-name {
  color: #fff;
  font-size: 14px;
}

.item-price {
  color: #00ffcc;
  font-weight: 600;
}

.cod-total {
  margin-top: 0.8rem;
  padding-top: 0.8rem;
  border-top: 2px solid rgba(0, 255, 204, 0.3);
  text-align: right;
  color: #00ffcc;
  font-size: 18px;
}

.cod-button {
  background: #ff6b35 !important;
}

.cod-button:hover {
  background: #ff8c42 !important;
  box-shadow: 0 0 10px #ff6b35, 0 0 40px #ff6b35 !important;
}

/* Checkout-Specific Styles */
.checkout-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.checkout-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.checkout-item-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  margin-right: 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.checkout-item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.checkout-item-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.checkout-item-quantity {
  color: #cccccc;
  font-size: 12px;
}

.checkout-item-price {
  color: #45f22e;
  font-weight: bold;
  font-size: 14px;
  margin-top: 4px;
}

/* Responsive design for checkout items */
@media (max-width: 768px) {
  .checkout-item {
    padding: 10px;
  }

  .checkout-item-image {
    width: 50px;
    height: 50px;
  }

  .checkout-item-name {
    font-size: 13px;
  }

  .checkout-item-quantity {
    font-size: 11px;
  }

  .checkout-item-price {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .checkout-item {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .checkout-item-image {
    width: 40px;
    height: 40px;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .checkout-item-details {
    width: 100%;
  }
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background: #000000b3;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 80px 0 20px 0;
  transition: right 0.3s ease-in-out;
  z-index: 2000;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  margin: 0;
  border: none;
}

.mobile-nav.active {
  right: 0;
}

.mobile-nav .nav-link {
  display: block;
  padding: 18px 20px;
  color: white;
  text-decoration: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 16px;
  transition: all 0.3s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.5);
}

.mobile-nav .nav-link:hover {
  background: rgba(0, 255, 204, 0.1);
  color: #00ffcc;
  padding-left: 30px;
}

/* Mobile overlay to close menu when clicking outside */
.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.mobile-nav-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Back Arrow Button - same design as mobile-nav-toggle */
.back-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 2001;
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}

.back-arrow {
  font-size: 20px;
  color: #00ffcc;
  font-weight: bold;
  transition: all 0.3s ease;
}

.back-arrow-btn:hover .back-arrow {
  color: white;
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .back-arrow-btn {
    left: 0.5rem;
    width: 25px;
    height: 25px;
  }

  .back-arrow {
    font-size: 18px;
  }

  /* Optimize canvas for maximum mobile performance and smooth interactions */
  canvas {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    /* Maximum performance 3D rendering optimizations - preserve original colors */
    image-rendering: auto; /* Use default rendering for better color accuracy */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
    /* GPU acceleration for smooth interactions */
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
    /* Optimize for 60fps */
    contain: layout style paint;
    isolation: isolate;
    /* Remove all color-altering filters to maintain original 3D model colors */
    /* Smooth transitions */
    transition: transform 0.1s ease-out;
  }

  /* Maximum performance mobile 3D optimizations */
  .product-details-container.mobile canvas {
    /* Remove color-altering filters to maintain original 3D model colors */
    /* Use default rendering for better color accuracy */
    image-rendering: auto;
    /* Force hardware acceleration */
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    /* Optimize for smooth interactions */
    pointer-events: auto;
    /* Reduce repaints during interaction */
    will-change: transform;
  }

  /* Performance boost during interaction */
  .product-details-container.mobile canvas[style*="--mobile-interaction: 1"] {
    /* Maintain color quality during interaction */
    image-rendering: auto;
    /* Remove color-altering filters during interaction */
  }

  /* Smooth scrolling and touch optimization */
  .product-details-container {
    -webkit-overflow-scrolling: touch;
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
  }

  /* Maximum performance mobile 3D model container optimizations */
  .product-details-container.mobile {
    /* Aggressive GPU acceleration for smoother 3D interactions */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform, opacity;
    /* Optimize for 3D rendering and smooth interactions */
    contain: layout style paint;
    isolation: isolate;
    /* Force compositing layer for better performance */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    /* Optimize touch handling */
    touch-action: manipulation;
    /* Reduce layout thrashing */
    overflow: hidden;
  }

  /* Improve mobile 3D model visibility and performance */
  @media (max-width: 768px) {
    /* Force hardware acceleration for 3D elements */
    .product-details-container,
    .product-details-container canvas,
    .product-details-container canvas > * {
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }

    /* Maximum performance rendering for mobile devices */
    .product-details-container canvas {
      /* Better mobile 3D model visibility */
      opacity: 1 !important;
      visibility: visible !important;
      /* Prevent flickering during interaction */
      -webkit-transform-style: preserve-3d;
      transform-style: preserve-3d;
      /* Ultra-smooth transitions */
      transition: filter 0.05s ease-out, transform 0.05s ease-out;
      /* Force hardware acceleration */
      transform: translate3d(0, 0, 0) rotateZ(0);
      -webkit-transform: translate3d(0, 0, 0) rotateZ(0);
      /* Optimize for 60fps interactions */
      will-change: transform, filter, opacity;
    }

    /* Immediate mobile interaction feedback for responsiveness */
    .product-details-container.mobile canvas:active {
      /* Remove color-altering filters to maintain original colors */
      transition: none; /* Remove transition during active interaction for immediate response */
    }

    /* Optimize touch events for maximum responsiveness */
    .product-details-container.mobile {
      /* Reduce touch delay */
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      /* Optimize touch handling */
      touch-action: pan-x pan-y;
      /* Prevent scrolling interference */
      overscroll-behavior: contain;
    }
  }
}

/* Mobile nav toggle button */
.mobile-nav-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 2001;
  position: relative;
}

/* Mobile cart badge on toggle button */
.mobile-toggle-cart-badge {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  background: #ff4444 !important;
  color: white !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  font-weight: bold !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  z-index: 2002 !important;
  animation: pulse 2s infinite !important;
}

.mobile-nav-toggle span {
  display: block;
  width: 100%;
  height: 3px;
  background-color: #00ffcc;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.mobile-nav-toggle.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.mobile-nav-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-nav-toggle.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

@media (max-width: 768px) {
  .mobile-nav-toggle {
    display: flex;
    order: 2;
    margin-left: auto;
  }

  .mobile-nav {
    width: 250px;
  }

  .mobile-nav .nav-link {
    padding: 15px 20px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .mobile-nav {
    width: 220px;
  }

  .mobile-nav .nav-link {
    padding: 12px 20px;
    font-size: 14px;
  }

  .mobile-toggle-cart-badge {
    width: 16px;
    height: 16px;
    font-size: 9px;
    top: -6px;
    right: -6px;
  }
}

/* Pulse animation for mobile cart badge */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
}

/* ===== MOBILE AUTH MODAL - SAME DESIGN AS PC ===== */

/* Mobile-specific auth modal adjustments */
@media (max-width: 768px) {
  .auth-overlay {
    padding: 1rem;
    box-sizing: border-box;
  }

  .auth-modal {
    /* Keep exact same design as PC but adjust sizing */
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 235, 0, 0.3);
    box-shadow: 0 4px 6px rgba(168, 86, 245, 0.5),
                0 4px 6px rgba(168, 86, 245, 0.5);
    backdrop-filter: blur(5px);
    border-radius: 20px;

    /* Mobile sizing adjustments */
    width: calc(100% - 2rem);
    max-width: 380px;
    padding: 1.5rem;
    margin: 0 auto;
    box-sizing: border-box;
  }

  .auth-modal:hover {
    /* Keep exact same hover effect as PC */
    box-shadow: 0 0 40px rgba(255, 235, 0, 0.5),
                0 0 60px rgb(255, 235, 0, 0.3);
  }

  /* Mobile title - same as PC */
  .auth-modal h2 {
    color: #fff !important;
    text-align: center;
    margin: 0 0 1.5rem 0;
    padding: 0;
    font-size: 1.6rem;
    text-shadow: 0 0 10px rgba(255, 235, 0, 0.7);
    background: transparent !important;
  }

  /* Mobile form groups - same spacing as PC */
  .form-group {
    margin-bottom: 1.2rem;
    position: relative;
  }

  /* Mobile labels - same as PC */
  .form-group label {
    display: block;
    margin-bottom: 0.6rem;
    color: #fff;
    font-size: 0.9rem;
    text-shadow: 0 0 8px rgba(255, 235, 0, 0.5);
  }

  /* Mobile inputs - same design as PC */
  .form-group input {
    width: 100%;
    padding: 0.9rem;
    border: 2px solid rgba(255, 235, 0, 0.3);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 235, 0, 0.2);
    box-sizing: border-box;
    -webkit-appearance: none;
    appearance: none;
  }

  .form-group input:focus {
    outline: none;
    border-color: #ffeb3b;
    box-shadow: 0 0 20px rgba(255, 235, 0, 0.5);
  }

  /* Mobile close button - same as PC */
  .close-auth {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #ffffff !important;
    font-size: 24px;
    cursor: pointer;
    text-shadow: none;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 1002;
    box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  }

  .close-auth:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
  }

  /* Mobile submit button - same as PC */
  .auth-submit {
    width: 100%;
    padding: 1rem;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
    display: block;
    margin: 1rem auto 0;
  }

  .auth-submit:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.7);
  }

  /* Mobile switch section - same as PC */
  .auth-switch {
    text-align: center;
    margin-top: 1rem;
  }

  .switch-btn {
    color: #4CAF50 !important;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 0.6rem 1rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    cursor: pointer;
    margin-top: 0.5rem;
  }

  .switch-btn:hover {
    text-shadow: 0 0 15px rgba(76, 175, 80, 0.7);
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .auth-overlay {
    padding: 0.75rem;
  }

  .auth-modal {
    width: calc(100% - 1.5rem);
    max-width: 340px;
    padding: 1.2rem;
  }

  .auth-modal h2 {
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .form-group input {
    padding: 0.8rem;
    font-size: 0.95rem;
  }

  .close-auth {
    width: 40px;
    height: 40px;
    font-size: 20px;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #ffffff !important;
    text-shadow: none;
    box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  }

  .auth-submit {
    padding: 0.9rem;
    font-size: 0.95rem;
  }

  .switch-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* ===== FINAL OVERRIDE - MAXIMUM SPECIFICITY FOR pageTitle FIXED POSITION ===== */
/* This rule has the highest specificity and should override ALL other styles */
html body div.App div.pageTitle,
html body div.App .pageTitle,
html body div .pageTitle,
html body .pageTitle,
body div.pageTitle,
body .pageTitle,
div.pageTitle,
.pageTitle,
*[class="pageTitle"],
[class="pageTitle"] {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 99999 !important;
  display: block !important;
  visibility: visible !important;
}

/* FORCE FIXED POSITION ON ALL SCREEN SIZES */
@media screen and (max-width: 768px) {
  html body div.App div.pageTitle,
  html body div.App .pageTitle,
  html body div .pageTitle,
  html body .pageTitle,
  body div.pageTitle,
  body .pageTitle,
  div.pageTitle,
  .pageTitle {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 99999 !important;
    display: block !important;
    visibility: visible !important;
  }
}

@media screen and (max-width: 480px) {
  html body div.App div.pageTitle,
  html body div.App .pageTitle,
  html body div .pageTitle,
  html body .pageTitle,
  body div.pageTitle,
  body .pageTitle,
  div.pageTitle,
  .pageTitle {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 99999 !important;
    display: block !important;
    visibility: visible !important;
  }

  /* Responsive styling for products page title */
  .products-page-title {
    font-size: 18px !important;
    padding: 12px 20px !important;
    max-width: 90% !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    margin-top: 0 !important; /* Remove top margin in mobile mode */
  }
}

/* Additional responsive styling for products page title */
@media screen and (max-width: 768px) {
  .products-page-title {
    font-size: 20px !important;
    padding: 12px 25px !important;
    margin-top: 0 !important; /* Remove top margin in mobile mode */
  }
}

/* Page Title */
.pageTitle {
  position: relative !important;
  color: white !important;
  padding: 10px 0 !important;
  font-size: 24px !important;
  font-weight: bold !important;
  text-align: center !important;
  text-shadow: 2px 2px 10px rgba(87, 5, 239, 0.8) !important;
  margin: 0 !important;
  background: transparent !important;
  margin-top: -20px !important;
}

/* Cart Container */
.cart-container {
  position: relative;
  max-width: 800px;
  margin: 80px auto 80px;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: auto;
  max-height: 90vh;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .pageTitle {
    margin-top: -15px !important;
    font-size: 20px !important;
    padding: 8px 0 !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    margin-top: -10px !important;
    font-size: 18px !important;
    padding: 6px 0 !important;
  }
}

.cart-items {
  margin-bottom: 0;
  flex: 1;
  overflow-y: auto;
  max-height: 70vh;
}

.cart-summary {
  padding: 1.5rem;
  text-align: center;
  color: white;
  margin-top: 0;
  flex-shrink: 0;
  background: transparent;
  border: none;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (max-width: 768px) {
  .cart-container {
    max-width: 95%;
    margin: 80px auto 2rem;
    padding: 1.5rem;
    max-height: 85vh;
    gap: 0.8rem;
  }

  .cart-items {
    max-height: 65vh;
    margin-bottom: 0;
  }

  .cart-summary {
    padding: 0.8rem;
    margin-top: 0;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .cart-container {
    margin: 60px auto 1rem;
    padding: 1rem;
    max-height: 80vh;
    gap: 0.5rem;
  }

  .cart-items {
    max-height: 60vh;
    margin-bottom: 0;
  }

  .cart-summary {
    padding: 0.6rem;
    margin-top: 0;
    gap: 0.3rem;
  }
}