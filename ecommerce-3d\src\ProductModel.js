import React, { useState, useEffect, useRef } from 'react';
import './App.css';

function ProductModel({ onInteraction }) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [modelScale, setModelScale] = useState(1); // Default scale
  const modelViewerRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    const modelViewer = modelViewerRef.current;

    const handleLoad = () => {
      console.log('3D Model loaded successfully');
      setIsLoading(false);
      setHasError(false);
      // Set initial scale
      if (modelViewerRef.current) {
        modelViewerRef.current.setAttribute('scale', `${modelScale} ${modelScale} ${modelScale}`);
      }
    };

    const handleError = (error) => {
      console.error('Error loading 3D model:', error);
      setIsLoading(false);
      setHasError(true);
    };

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        console.warn('3D Model loading timeout');
        setIsLoading(false);
        setHasError(true);
      }
    }, 15000); // 15 seconds timeout

    if (modelViewer) {
      modelViewer.addEventListener('load', handleLoad);
      modelViewer.addEventListener('error', handleError);
    }

    return () => {
      clearTimeout(loadingTimeout);
      if (modelViewer) {
        modelViewer.removeEventListener('load', handleLoad);
        modelViewer.removeEventListener('error', handleError);
      }
    };
  }, [isLoading, modelScale]);

  useEffect(() => {
    if (!containerRef.current) return;

    const handleInteractionStart = () => {
      onInteraction?.(true);
    };

    const handleInteractionEnd = () => {
      onInteraction?.(false);
    };

    const container = containerRef.current;
    container.addEventListener('mousedown', handleInteractionStart);
    container.addEventListener('touchstart', handleInteractionStart);
    container.addEventListener('mouseup', handleInteractionEnd);
    container.addEventListener('touchend', handleInteractionEnd);
    container.addEventListener('mouseleave', handleInteractionEnd);

    return () => {
      container.removeEventListener('mousedown', handleInteractionStart);
      container.removeEventListener('touchstart', handleInteractionStart);
      container.removeEventListener('mouseup', handleInteractionEnd);
      container.removeEventListener('touchend', handleInteractionEnd);
      container.removeEventListener('mouseleave', handleInteractionEnd);
    };
  }, [onInteraction]);

  // Scale control functions
  const increaseScale = () => {
    const newScale = Math.min(modelScale + 0.2, 3); // Max scale 3x
    setModelScale(newScale);
    if (modelViewerRef.current) {
      modelViewerRef.current.setAttribute('scale', `${newScale} ${newScale} ${newScale}`);
    }
  };

  const decreaseScale = () => {
    const newScale = Math.max(modelScale - 0.2, 0.3); // Min scale 0.3x
    setModelScale(newScale);
    if (modelViewerRef.current) {
      modelViewerRef.current.setAttribute('scale', `${newScale} ${newScale} ${newScale}`);
    }
  };

  const resetScale = () => {
    setModelScale(1); // Reset to default scale
    if (modelViewerRef.current) {
      modelViewerRef.current.setAttribute('scale', '1 1 1');
    }
  };

  return (
    <div style={{ 
      height: '100vh', // Full viewport height
      width: '100vw', 
      position: 'relative', 
      overflow: 'visible', // No clipping
      zIndex: 0 // Behind navbar/footer
    }}>
      {hasError && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          zIndex: 10,
          width: '300px',
          height: '300px'
        }}>
          <img
            src="/iPhone-14-Pro-Max.png"
            alt="iPhone 14 Pro Max"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              filter: 'drop-shadow(0 0 20px rgba(255,255,255,0.3))'
            }}
          />
          <div style={{
            color: 'white',
            marginTop: '10px',
            background: 'rgba(0,0,0,0.8)',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '14px'
          }}>
            3D model unavailable - showing 2D image
          </div>
        </div>
      )}

      {isLoading && !hasError && (
        <div className="socket">
          <div className="gel center-gel">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c1 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c2 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c3 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c4 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c5 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c6 r1">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c7 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c8 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c9 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c10 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c11 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c12 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c13 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c14 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c15 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c16 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c17 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c18 r2">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c19 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c20 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c21 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c22 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c23 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c24 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c25 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c26 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c27 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c28 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c29 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c30 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c31 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c32 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c33 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c34 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c35 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c36 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
          <div className="gel c37 r3">
            <div className="hex-brick h1"></div>
            <div className="hex-brick h2"></div>
            <div className="hex-brick h3"></div>
          </div>
        </div>
      )}

      {/* Full-size model-viewer for rendering */}
      <model-viewer
        ref={modelViewerRef}
        src="/iphone_14_pro_max.glb"
        alt="iPhone 14 Pro Max"
        auto-rotate
        camera-controls
        auto-rotate-delay="1000"
        rotation-per-second="30deg"
        interaction-prompt="none"
        loading="eager"
        reveal="auto"
        style={{
          width: '100%',
          height: '100%',
          position: 'fixed',
          top: 0,
          left: 0,
          zIndex: -1,
          display: hasError ? 'none' : 'block'
        }}
      >
        <div slot="progress-bar" style={{ display: 'none' }}></div>
      </model-viewer>

      {/* 3D Model Size Controls */}
      {!hasError && !isLoading && (
        <div className="model-size-controls">
          <button
            className="size-control-btn size-decrease"
            onClick={decreaseScale}
            title="Decrease model size"
            aria-label="Decrease 3D model size"
          >
            −
          </button>
          <button
            className="size-control-btn size-reset"
            onClick={resetScale}
            title="Reset model size"
            aria-label="Reset 3D model to original size"
          >
            ⌂
          </button>
          <button
            className="size-control-btn size-increase"
            onClick={increaseScale}
            title="Increase model size"
            aria-label="Increase 3D model size"
          >
            +
          </button>
        </div>
      )}

      {/* Mask to show only 80vw x 80vh */}
      <div style={{
        width: '100vw',
        height: '100vh',
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        overflow: 'hidden',
        zIndex: 0,
        pointerEvents: 'none' // Allows interaction with model
      }}></div>
    </div>
  );
}

export default ProductModel;