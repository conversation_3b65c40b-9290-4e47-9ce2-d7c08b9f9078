import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import * as THREE from 'three';
import api from './api/axios';
import './App.css';

const HexagonLoader = () => {
  return (
    <div className="socket">
      <div className="gel center-gel">
        <div className="hex-brick h1"></div>
        <div className="hex-brick h2"></div>
        <div className="hex-brick h3"></div>
      </div>
      {[...Array(36)].map((_, i) => (
        <div key={i} className={`gel c${i + 1} r${(i % 3) + 1}`}>
          <div className="hex-brick h1"></div>
          <div className="hex-brick h2"></div>
          <div className="hex-brick h3"></div>
        </div>
      ))}
    </div>
  );
};

function Model({ url, id, onModelLoaded }) {
  const [model, setModel] = useState(null);

  useEffect(() => {
    const loader = new GLTFLoader();
    let isMounted = true;

    // Check if URL exists before attempting to load
    if (!url) {
      console.warn(`No model URL provided for product ID: ${id}`);
      onModelLoaded();
      return;
    }

    loader.load(
      url,
      (gltf) => {
        if (!isMounted) return; // Prevent state update if component unmounted

        const loadedModel = gltf.scene;

        // Aggressive performance optimization for smooth mobile interaction
        loadedModel.traverse((child) => {
          if (child.isMesh) {
            // Enable frustum culling for better performance
            child.frustumCulled = true;

            // Disable shadows completely for performance
            child.castShadow = false;
            child.receiveShadow = false;

            // Preserve original materials completely - no modifications for color accuracy
            if (child.material) {
              // Only apply essential performance optimizations that don't affect appearance
              child.material.precision = 'highp'; // Use high precision for better color accuracy

              // Keep original material properties intact for authentic colors
              // No modifications to metalness, roughness, emissive, or other visual properties

              // Only optimize geometry if needed for performance
              if (child.geometry && window.innerWidth <= 768) {
                // Minimal geometry optimization for mobile performance only
                if (child.geometry.attributes.uv2) {
                  child.geometry.deleteAttribute('uv2');
                }
              }
            }
          }
        });
        switch (id) {
          case '1': loadedModel.scale.set(42, 42, 42); break;
          case '2': 
            loadedModel.scale.set(1.50, 1.50, 1.50);
            loadedModel.position.set(1.6, -3.2, 0);
            loadedModel.rotation.y = Math.PI;
            break;
          case '3': 
            loadedModel.scale.set(3.5, 3.5, 3.5);
            loadedModel.position.set(0, 1, 1);
            break;
          case '4': 
            loadedModel.scale.set(0.017, 0.017, 0.017);
            loadedModel.position.set(0, -2, 0);
            loadedModel.rotation.set(0, Math.PI / 1.5, 0);
            break;
          case '5': // Apple Vision Pro - REAL MODEL
            loadedModel.scale.set(8, 8, 8);
            loadedModel.position.set(0, -1, 0);
            loadedModel.rotation.set(0.2, 0, 0);
            break;
          case '6': loadedModel.scale.set(2, 2, 2); break;
          case '7': 
            loadedModel.scale.set(5, 5, 5);
            loadedModel.position.set(0, -3.1, 0);
            const quaternionVR = new THREE.Quaternion();
            quaternionVR.setFromEuler(new THREE.Euler(0.2, THREE.MathUtils.degToRad(35), 0));
            loadedModel.quaternion.copy(quaternionVR);
            break;
          case '8': 
            loadedModel.scale.set(1.5, 1.5, 1.5);
            loadedModel.position.set(0, -1.3, 0);
            break;
          case '9': 
            loadedModel.scale.set(15, 15, 15);
            loadedModel.position.set(0, -2, 0);
            break;
          case '10': // Logitech Mouse
            loadedModel.scale.set(45, 45, 45);
            loadedModel.position.set(0, -1.8, 0);
            loadedModel.rotation.set(0, Math.PI / 1.5, 0.2);
            break;
          case '11': 
            loadedModel.scale.set(20, 20, 20);
            loadedModel.position.set(0, -3, 0);
            break;
          case '12': 
            loadedModel.scale.set(0.1, 0.1, 0.1);
            loadedModel.position.set(0, -1, 0);
            break;
          case '13': 
            loadedModel.scale.set(0.08, 0.08, 0.08);
            loadedModel.position.set(0, -1, 0);
            loadedModel.rotation.x = Math.PI / 6;
            break;
          case '14': 
            loadedModel.scale.set(82, 82, 82);
            loadedModel.position.set(0, -6.5, 0);
            loadedModel.rotation.y = Math.PI;
            break;
          case '15': // ASUS ROG Strix Scar 17 - REAL MODEL
            loadedModel.scale.set(2.5, 2.5, 2.5);
            loadedModel.position.set(0, -8, 0);
            loadedModel.rotation.y = Math.PI / 8; // Slight angle for better view
            break;
          default: loadedModel.scale.set(1, 1, 1); break;
        }

        setModel(loadedModel);
        onModelLoaded();
      },
      (progress) => {
        // Handle loading progress with safety check
        if (progress.total && progress.total > 0) {
          const percentage = Math.round((progress.loaded / progress.total) * 100);
          console.log('Loading progress:', percentage + '%');
        } else {
          console.log('Loading progress: calculating...');
        }
      },
      (error) => {
        console.error(`Error loading 3D model for product ${id}:`, error);
        console.error('Model URL:', url);
        if (isMounted) onModelLoaded();
      }
    );

    return () => {
      isMounted = false;
    };
  }, [url, id, onModelLoaded]);

  // Separate cleanup effect for model disposal
  useEffect(() => {
    return () => {
      if (model) {
        model.traverse(child => {
          if (child.isMesh) {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach(material => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        });
      }
    };
  }, [model]);

  return model ? <primitive object={model} /> : null;
}

const productCache = new Map();

function ProductDetails() {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [isApiLoading, setIsApiLoading] = useState(true);
  const [isModelLoading, setIsModelLoading] = useState(true);
  const [error, setError] = useState('');
  const [showBlur, setShowBlur] = useState(true);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [isProductWindowOpen, setIsProductWindowOpen] = useState(false);
  const [isModelInteracting, setIsModelInteracting] = useState(false);

  const isLoggedIn = !!localStorage.getItem('token');

  const productModels = {
    1: "/iphone_16_pro_max.glb",                                                    // ✅ iPhone 16 Pro Max
    2: "/samsung_s24_ultra.glb",                                                   // ✅ Samsung S24 Ultra
    3: "/apple-macbook-pro.glb",                                                   // ✅ MacBook Pro
    4: "/playstation_5.glb",                                                       // ✅ PlayStation 5
    5: "/apple_vision_pro.glb",                                                    // ✅ Apple Vision Pro - REAL MODEL
    6: "/apple_watch_series_6.glb",                                               // ✅ Apple Watch Series 6
    7: "/RGS_VR_headset.glb",                                                     // ✅ VR Headset
    8: "/canon_800d.glb",                                                         // ✅ Canon Camera (Drone substitute)
    9: "/smart_tv.glb",                                                           // ✅ Smart TV (Xbox substitute)
    10: "/01- Logitech.G502.Lightspeed/d1566f772ed14e7c87d1a803774911da.gltf",   // ✅ Logitech Mouse
    11: "/msi_gaming_headset.glb",                                                 // ✅ MSI Gaming Headset
    12: "/Msi LapTop.glb",                                                        // ✅ MSI Laptop
    13: "/samsung_galaxy_AirPods.glb",                                            // ✅ Samsung AirPods
    14: "/iphone_14_pro_max.glb",                                                 // ✅ iPhone 14 Pro Max
    15: "/Asus_ROG_Strix_Scar_17.glb",                                           // ✅ ASUS ROG Strix Scar 17 - REAL MODEL
  };

  useEffect(() => {
    const fetchProduct = async () => {
      if (productCache.has(id)) {
        setProduct(productCache.get(id));
        setIsApiLoading(false);
        return;
      }

      try {
        const response = await api.get(`/products/${id}`, {
          headers: {
            'Cache-Control': 'no-cache'
          }
        });
        setProduct(response.data);
        productCache.set(id, response.data);
      } catch (err) {
        console.warn('API not available, using fallback product data:', err.message);
        // Use fallback product data when API is not available with correct image paths
        const fallbackProducts = {
          1: {
            id: 1,
            name: "iPhone 16 Pro Max",
            image: "/uploads/iPhone-16-Pro-& pro maxlarge.png",
            price: "$1199",
            description: "Latest iPhone with advanced camera system",
            specs: ["6.7-inch display", "A18 Pro chip", "Pro camera system"]
          },
          2: {
            id: 2,
            name: "Samsung Galaxy S24 Ultra",
            image: "/uploads/s24 ultra.PNG",
            price: "$1299",
            description: "Premium Android smartphone",
            specs: ["6.8-inch display", "S Pen included", "200MP camera"]
          },
          3: {
            id: 3,
            name: "MacBook Pro",
            image: "/uploads/Apple-Macbook-Pro-Transparent-Image.png",
            price: "$2499",
            description: "Powerful laptop for professionals",
            specs: ["M3 Pro chip", "16-inch display", "32GB RAM"]
          },
          4: {
            id: 4,
            name: "PlayStation 5",
            image: "/uploads/Sony PlayStation 5.jpg",
            price: "$499",
            description: "Next-generation gaming console",
            specs: ["4K gaming", "Ray tracing", "SSD storage"]
          },
          5: {
            id: 5,
            name: "Apple Vision Pro",
            image: "/uploads/Apple-WWDC23-Vision-Pro-glass-230605_big.jpg.large.png",
            price: "$3499",
            description: "Revolutionary mixed reality headset",
            specs: ["Spatial computing", "Eye tracking", "Hand tracking"]
          },
          6: {
            id: 6,
            name: "Apple Watch Series 7",
            image: "/uploads/Apple Watch Series 7.jpg",
            price: "$399",
            description: "Advanced smartwatch with health monitoring",
            specs: ["Always-on display", "Blood oxygen monitoring", "GPS"]
          },
          7: {
            id: 7,
            name: "VR Headset",
            image: "/uploads/RGS_VR_headset.png",
            price: "$599",
            description: "Immersive virtual reality experience",
            specs: ["4K display", "6DOF tracking", "Wireless"]
          },
          8: {
            id: 8,
            name: "Canon Camera",
            image: "/uploads/canon_800.png",
            price: "$1299",
            description: "Professional DSLR camera",
            specs: ["4K video", "High resolution", "Professional lens"]
          },
          9: {
            id: 9,
            name: "Xbox Series X",
            image: "/uploads/pngimg.com - xbox_PNG101375.png",
            price: "$499",
            description: "Most powerful Xbox console",
            specs: ["4K gaming", "120fps", "Quick Resume"]
          },
          10: {
            id: 10,
            name: "Logitech G502 Lightspeed",
            image: "/uploads/Logitech G502 Lightspeed.png",
            price: "$149",
            description: "Wireless gaming mouse",
            specs: ["25K DPI sensor", "Wireless", "11 programmable buttons"]
          },
          11: {
            id: 11,
            name: "MSI Gaming Headset",
            image: "/uploads/msi headset.png",
            price: "$199",
            description: "Professional gaming headset",
            specs: ["7.1 surround sound", "RGB lighting", "Noise cancelling"]
          },
          12: {
            id: 12,
            name: "MSI Laptop",
            image: "/uploads/msi NBG.png",
            price: "$1899",
            description: "High-performance gaming laptop",
            specs: ["RTX 4070", "Intel i7", "16GB RAM"]
          },
          13: {
            id: 13,
            name: "Samsung Galaxy AirPods",
            image: "/uploads/Samsung airpods.png",
            price: "$199",
            description: "Wireless earbuds with ANC",
            specs: ["Active noise cancelling", "Wireless charging", "8-hour battery"]
          },
          14: {
            id: 14,
            name: "iPhone 14 Pro Max",
            image: "/uploads/iPhone-14-Pro-Max.png",
            price: "$1099",
            description: "Previous generation iPhone Pro",
            specs: ["6.7-inch display", "A16 Bionic chip", "Pro camera system"]
          },
          15: {
            id: 15,
            name: "ASUS ROG Strix Scar 17",
            image: "/uploads/Asus ROG Strix Scar 17.png",
            price: "$2299",
            description: "Ultimate gaming laptop",
            specs: ["RTX 4080", "AMD Ryzen 9", "32GB RAM"]
          }
        };

        const fallbackProduct = fallbackProducts[parseInt(id)] || {
          id: parseInt(id),
          name: `Product ${id}`,
          image: "/uploads/placeholder.png",
          price: "$999",
          description: "Product description not available",
          specs: ["Specification not available"]
        };
        setProduct(fallbackProduct);
        setError(''); // Clear error since we have fallback data
      } finally {
        setIsApiLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 768);
      setIsTablet(width > 768 && width <= 1024);
    };

    // Throttle resize events for better performance
    let resizeTimeout;
    const throttledResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(checkDeviceType, 100);
    };

    checkDeviceType();
    window.addEventListener('resize', throttledResize, { passive: true });

    document.body.style.overflow = 'hidden';
    const timer = setTimeout(() => {
      setShowBlur(false);
    }, 3000);

    return () => {
      document.body.style.overflow = 'visible';
      clearTimeout(timer);
      clearTimeout(resizeTimeout);
      window.removeEventListener('resize', throttledResize);
    };
  }, []);

  // Performance optimization effect for mobile
  useEffect(() => {
    if (isMobile) {
      // Optimize browser for 3D performance
      const optimizePerformance = () => {
        // Request high performance mode
        if ('requestIdleCallback' in window) {
          window.requestIdleCallback(() => {
            // Force GPU acceleration
            document.body.style.transform = 'translateZ(0)';
            document.body.style.backfaceVisibility = 'hidden';
          });
        }

        // Optimize memory usage
        if ('gc' in window && typeof window.gc === 'function') {
          setTimeout(() => window.gc(), 1000);
        }
      };

      optimizePerformance();

      // Cleanup on unmount
      return () => {
        document.body.style.transform = '';
        document.body.style.backfaceVisibility = '';
      };
    }
  }, [isMobile]);

  // Handle modal and product window interaction
  useEffect(() => {
    // When modal opens, ensure product window is hidden
    if (showDetailModal) {
      // Add a class to body to prevent scrolling when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore scrolling when modal is closed
      document.body.style.overflow = 'hidden'; // Keep hidden for product details page
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'visible';
    };
  }, [showDetailModal]);

  const getCameraPosition = (currentId) => {
    switch(currentId) {
      case '5': return [0, 0, 35];
      case '14': return [0, 0, 20];
      default: return [0, 0, 10];
    }
  };

  const { originalPrice, discountedPrice } = useMemo(() => {
    const orig = product ? parseFloat(product.price.replace('$', '')) : 0;
    return {
      originalPrice: orig,
      discountedPrice: isLoggedIn ? (orig * 0.85).toFixed(2) : orig
    };
  }, [product, isLoggedIn]);

  const isLoading = isModelLoading;

  const addToCart = (prod) => {
    console.log('🛒 PRODUCT DETAILS: Adding product to cart:', prod);
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    console.log('🛒 PRODUCT DETAILS: Current cart before adding:', cart);

    // Ensure product has a unique identifier
    const productId = prod._id || prod.id;
    if (!productId) {
      console.error('Product missing ID:', prod);
      alert('Error: Product ID is missing. Cannot add to cart.');
      return;
    }
    console.log('🛒 PRODUCT DETAILS: Using product ID:', productId);

    // Create a normalized product object with consistent ID structure
    const normalizedProduct = {
      ...prod,
      id: productId,
      _id: productId,
      price: originalPrice,
      quantity: 1
    };

    const existingItem = cart.find(item => {
      const itemId = item._id || item.id;
      return itemId === productId;
    });

    if (existingItem) {
      console.log('🛒 PRODUCT DETAILS: Found existing item, incrementing quantity:', existingItem);
      existingItem.quantity += 1;
    } else {
      console.log('🛒 PRODUCT DETAILS: Adding new item to cart:', normalizedProduct);
      cart.push(normalizedProduct);
    }

    console.log('🛒 PRODUCT DETAILS: Final cart after adding:', cart);
    localStorage.setItem('cart', JSON.stringify(cart));

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));

    alert(`${prod.name} added to cart!`);
  };

  const handleZoomIn = () => {
    // Zoom functionality can be implemented later if needed
  };

  const handleZoomOut = () => {
    // Zoom functionality can be implemented later if needed
  };

  const handleResetView = () => {
    // Reset view functionality can be implemented later if needed
  };

  // Touch gesture handlers for mobile - optimized for performance
  const handleTouchStart = (e) => {
    if (!isMobile && !isTablet) return;

    // Don't interfere with 3D model interaction
    if (e.target.closest('canvas')) return;

    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchEnd = (e) => {
    if ((!isMobile && !isTablet) || !touchStart) return;

    // Don't interfere with 3D model interaction
    if (e.target.closest('canvas')) {
      setTouchStart(null);
      return;
    }

    const endTouch = e.changedTouches[0].clientX;
    const diff = touchStart - endTouch;
    const swipeThreshold = 50;

    // Use requestAnimationFrame for smooth animations
    requestAnimationFrame(() => {
      // Swipe right to left to show product window (from right edge)
      if (diff > swipeThreshold && touchStart > window.innerWidth - 80) {
        setIsProductWindowOpen(true);
      }
      // Swipe left to right to hide product window
      else if (diff < -swipeThreshold && isProductWindowOpen) {
        setIsProductWindowOpen(false);
      }
    });

    setTouchStart(null);
  };

  const handleBackgroundClick = (e) => {
    // Close product window when clicking outside of it
    if (isProductWindowOpen && !e.target.closest('.product-window')) {
      setIsProductWindowOpen(false);
    }
  };

  if (error) return <div className="error-message">{error}</div>;
  if (!product && !isApiLoading) return <div className="no-product">Product not found</div>;

  return (
    <div
      className={`product-details-container ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={handleBackgroundClick}
    >
      {showBlur && <div className="background blur"></div>}

      {isLoading && (
        <div className="loader-container">
          <HexagonLoader />
          <div className="loading-text">Loading Model...</div>
        </div>
      )}



      {/* Swipe Indicator for Mobile/Tablet - copied from Home.js */}
      {(isMobile || isTablet) && !isProductWindowOpen && (
        <div className={`swipe-indicator ${isModelInteracting ? 'fade-out' : ''}`}>
          <div className="swipe-arrow">←</div>
          <span className="swipe-text">Swipe from right edge</span>
        </div>
      )}

      <Canvas
        style={{
          height: '100vh',
          width: '100%',
          opacity: isLoading ? 0 : 1,
          transition: 'opacity 0.5s ease',
          background: 'transparent'
        }}
        camera={{
          position: getCameraPosition(id),
          fov: isMobile ? 60 : 50,
          near: 0.1,
          far: 100
        }}
        gl={{
          antialias: true, // Enable antialiasing for better visual quality
          alpha: true, // Enable transparency to show background
          powerPreference: 'high-performance', // Use high performance for better color rendering
          stencil: false,
          depth: true,
          logarithmicDepthBuffer: false,
          toneMapping: THREE.LinearToneMapping, // Use linear tone mapping for most accurate colors
          toneMappingExposure: 1.0, // Standard exposure for accurate colors
          outputColorSpace: THREE.SRGBColorSpace, // Ensure proper color space
          physicallyCorrectLights: true, // Enable for more accurate lighting and colors
          preserveDrawingBuffer: false,
          failIfMajorPerformanceCaveat: false
        }}
        dpr={Math.min(window.devicePixelRatio, 2)} // Consistent pixel ratio for all devices
        performance={{
          min: 0.5, // Consistent performance threshold
          max: 1.0, // Allow maximum performance for better quality
          debounce: 100 // Consistent debounce for all devices
        }}
        frameloop="always" // Always render for consistent appearance
      >
        {/* Minimal, color-accurate lighting setup for authentic model appearance */}
        <ambientLight intensity={0.6} color="#ffffff" />

        {/* Single main directional light for natural illumination */}
        <directionalLight
          position={[10, 10, 5]}
          intensity={0.8}
          color="#ffffff"
          castShadow={false}
        />

        {/* Subtle fill light to prevent harsh shadows */}
        <directionalLight
          position={[-5, 5, -5]}
          intensity={0.2}
          color="#ffffff"
          castShadow={false}
        />

        {/* Use studio environment for consistent, neutral lighting across devices */}
        <Environment preset="studio" />
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          enableDamping={true}
          dampingFactor={isMobile ? 0.05 : 0.05} // Lower damping for more responsive feel
          zoomSpeed={isMobile ? 1.2 : 1.0} // Faster zoom for better responsiveness
          panSpeed={isMobile ? 1.2 : 1.0} // Faster pan for better responsiveness
          rotateSpeed={isMobile ? 1.5 : 1.0} // Faster rotation for better responsiveness
          minDistance={2}
          maxDistance={50}
          touches={{
            ONE: THREE.TOUCH.ROTATE,
            TWO: THREE.TOUCH.DOLLY_PAN
          }}
          mouseButtons={{
            LEFT: THREE.MOUSE.ROTATE,
            MIDDLE: THREE.MOUSE.DOLLY,
            RIGHT: THREE.MOUSE.PAN
          }}
          onStart={() => {
            setIsModelInteracting(true);
            // Aggressive performance optimization during interaction
            if (isMobile) {
              document.body.style.willChange = 'transform';
              document.body.style.userSelect = 'none';
              document.body.style.touchAction = 'none';
              // Reduce rendering quality during interaction for smoothness
              document.documentElement.style.setProperty('--mobile-interaction', '1');
            }
          }}
          onEnd={() => {
            setIsModelInteracting(false);
            // Restore after interaction with slight delay for smoothness
            if (isMobile) {
              setTimeout(() => {
                document.body.style.willChange = 'auto';
                document.body.style.userSelect = 'auto';
                document.body.style.touchAction = 'auto';
                document.documentElement.style.setProperty('--mobile-interaction', '0');
              }, 100);
            }
          }}
          // Optimized interaction settings for smoothness
          autoRotate={false}
          autoRotateSpeed={0}
          screenSpacePanning={true}
          keyPanSpeed={7.0}
          maxPolarAngle={Math.PI} // Allow full rotation
          minPolarAngle={0}
          // Reduce update frequency for better performance
          target0={[0, 0, 0]}
          position0={getCameraPosition(id)}
          zoom0={1}
        />

        {productModels[id] ? (
          <Model
            url={productModels[id]}
            id={id}
            onModelLoaded={() => setIsModelLoading(false)}
          />
        ) : (
          // Fallback for products without 3D models
          <mesh onUpdate={() => setIsModelLoading(false)}>
            <boxGeometry args={[2, 2, 2]} />
            <meshStandardMaterial color="#666" />
          </mesh>
        )}
      </Canvas>

      {!isApiLoading && product && (
        <div
          className={`product-window ${
            showDetailModal
              ? 'move-right hidden modal-open' // Hide when modal is open with modal-open class
              : (isMobile || isTablet
                ? (isProductWindowOpen ? 'move-right visible' : 'move-right hidden')
                : 'move-right visible')
          }`}
          style={{
            zIndex: showDetailModal ? 5 : 10, // Lower z-index when modal is open
            display: showDetailModal ? 'none' : 'flex', // Completely hide when modal is open
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={() => !showDetailModal && setShowDetailModal(true)} // Prevent click when modal is open
        >
          <img
            src={product.image}
            alt={product.name}
            className="product-image"
            onError={(e) => {
              console.error(`Failed to load product image: ${product.image}`);
              // Try multiple fallback options
              if (!e.target.dataset.fallbackAttempted) {
                e.target.dataset.fallbackAttempted = 'true';
                // First fallback: try iPhone image
                e.target.src = '/uploads/iPhone-16-Pro-& pro maxlarge.png';
              } else if (!e.target.dataset.secondFallbackAttempted) {
                e.target.dataset.secondFallbackAttempted = 'true';
                // Second fallback: try iPhone 14 Pro Max
                e.target.src = '/uploads/iPhone-14-Pro-Max.png';
              } else {
                // Final fallback: hide image and show placeholder text
                e.target.style.display = 'none';
                const placeholder = document.createElement('div');
                placeholder.innerHTML = '📱<br>Image Not Available';
                placeholder.style.cssText = 'color: white; text-align: center; font-size: 2rem; padding: 40px;';
                e.target.parentNode.insertBefore(placeholder, e.target);
              }
            }}
            onLoad={() => {
              console.log(`Product image loaded successfully: ${product.image}`);
            }}
          />
          <h2 className="product-title">{product.name}</h2>
          <p className="price">
            {isLoggedIn ? (
              <>
                <span style={{ color: 'red', textDecoration: 'line-through' }}>
                  ${originalPrice}
                </span>{' '}
                <span style={{ color: 'green' }}>${discountedPrice}</span>
              </>
            ) : (
              `$${originalPrice}`
            )}
          </p>
          <p className="product-description">{product.description}</p>
          <button 
            className="nav-link" 
            onClick={(e) => {
              e.stopPropagation();
              addToCart(product);
            }}
          >
            Add to Cart
          </button>
        </div>
      )}

      {showDetailModal && product && (
        <div className="modal-overlay" onClick={() => setShowDetailModal(false)}>
          <div className="product-modal" onClick={(e) => e.stopPropagation()}>
            <button className="close-btn" onClick={() => setShowDetailModal(false)}>
              ×
            </button>
            <img
              src={product.image}
              alt={product.name}
              className="modal-image"
              onError={(e) => {
                console.error(`Failed to load modal image: ${product.image}`);
                if (!e.target.dataset.modalFallbackAttempted) {
                  e.target.dataset.modalFallbackAttempted = 'true';
                  e.target.src = '/uploads/iPhone-16-Pro-& pro maxlarge.png';
                } else {
                  e.target.src = '/uploads/iPhone-14-Pro-Max.png';
                }
              }}
            />
            <div className="modal-content">
              <h2>{product.name}</h2>
              <h3 className="modal-price">
                {isLoggedIn ? (
                  <>
                    <span style={{ color: 'red', textDecoration: 'line-through' }}>
                      ${originalPrice}
                    </span>{' '}
                    <span style={{ color: 'green' }}>${discountedPrice}</span>
                  </>
                ) : (
                  `$${originalPrice}`
                )}
              </h3>
              <div className="specs-list">
                {product.specs?.map((spec, index) => (
                  <div key={index} className="spec-item">✓ {spec}</div>
                ))}
              </div>
              <p className="modal-description">{product.description}</p>
              <button 
                className="nav-link" 
                onClick={() => addToCart(product)}
              >
                Add to Cart
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ProductDetails;