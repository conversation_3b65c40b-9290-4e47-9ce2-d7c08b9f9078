# Deployment Guide

## Local Development

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Setup
1. Navigate to the project directory:
   ```bash
   cd ecommerce-3d
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm start
   ```

4. Open browser to `http://localhost:3000`

### Local Testing
- Products page: `http://localhost:3000/products`
- Individual product: `http://localhost:3000/product/1`
- The app will work with fallback data even without a backend API

## Production Deployment on Render

### Current Configuration
- **Frontend URL**: `https://threed-e-commerce-front.onrender.com`
- **Backend URL**: `https://threed-e-commerce-backend.onrender.com`

### Deployment Steps

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Fix product page and API issues"
   git push origin main
   ```

2. **Render Auto-Deploy**:
   - Render will automatically detect changes and redeploy
   - Build command: `npm install && npm run build`
   - Start command: `npx serve -s build -l 10000`

3. **Environment Variables** (Already configured in render.yaml):
   - `NODE_ENV=production`
   - `GENERATE_SOURCEMAP=false`
   - `REACT_APP_API_URL=https://threed-e-commerce-backend.onrender.com/api`
   - `REACT_APP_ENVIRONMENT=production`

### Verification Steps

1. **Check Frontend**:
   - Visit: `https://threed-e-commerce-front.onrender.com/products`
   - Verify products are loading (either from API or fallback)
   - Check browser console for errors

2. **Check API Connection**:
   - Open browser dev tools
   - Look for API calls to the backend
   - If API fails, fallback products should display

3. **Test 3D Models**:
   - Click on individual products
   - 3D models should load or show fallback geometry
   - No GLTF parsing errors in console

## Troubleshooting

### Common Issues

1. **Build Fails**:
   - Check if all dependencies are installed
   - Verify Node.js version compatibility
   - Check for syntax errors in code

2. **API Connection Issues**:
   - Verify backend is running at correct URL
   - Check CORS configuration on backend
   - Ensure environment variables are set correctly

3. **3D Model Loading Issues**:
   - Check if GLB files exist in public directory
   - Verify file paths are correct
   - Models will fallback to cube geometry if missing

4. **Render Deployment Issues**:
   - Check build logs in Render dashboard
   - Verify render.yaml configuration
   - Ensure all environment variables are set

### Environment Variables

**Development (.env.development)**:
```
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_ENVIRONMENT=development
GENERATE_SOURCEMAP=false
ESLINT_NO_DEV_ERRORS=true
```

**Production (.env.production)**:
```
REACT_APP_API_URL=https://threed-e-commerce-backend.onrender.com/api
REACT_APP_ENVIRONMENT=production
GENERATE_SOURCEMAP=false
ESLINT_NO_DEV_ERRORS=true
```

## Performance Optimizations

1. **Source Maps Disabled**: Reduces build size
2. **Memory Optimization**: Build uses limited memory allocation
3. **Fallback Data**: Ensures fast loading even without API
4. **Error Boundaries**: Graceful handling of component failures

## Monitoring

### What to Monitor
1. **API Response Times**: Check backend performance
2. **3D Model Loading**: Monitor GLTF loading success rates
3. **Error Rates**: Watch for JavaScript errors in production
4. **User Experience**: Ensure fallback data provides good UX

### Logs to Check
1. **Render Build Logs**: For deployment issues
2. **Browser Console**: For frontend errors
3. **Network Tab**: For API call failures
4. **Performance Tab**: For loading performance
