const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  let filePath = path.join(__dirname, 'build', req.url === '/' ? 'index.html' : req.url);
  
  // If file doesn't exist, serve index.html for SPA routing
  if (!fs.existsSync(filePath)) {
    filePath = path.join(__dirname, 'build', 'index.html');
  }

  const ext = path.extname(filePath);
  let contentType = 'text/html';
  
  switch (ext) {
    case '.js':
      contentType = 'application/javascript';
      break;
    case '.css':
      contentType = 'text/css';
      break;
    case '.json':
      contentType = 'application/json';
      break;
    case '.png':
      contentType = 'image/png';
      break;
    case '.jpg':
    case '.jpeg':
      contentType = 'image/jpeg';
      break;
    case '.ico':
      contentType = 'image/x-icon';
      break;
    case '.glb':
      contentType = 'model/gltf-binary';
      break;
  }

  fs.readFile(filePath, (err, data) => {
    if (err) {
      console.error('Error reading file:', filePath, err);
      res.writeHead(404);
      res.end('Not found');
    } else {
      res.writeHead(200, {'Content-Type': contentType});
      res.end(data);
      console.log(`Served: ${req.url} -> ${filePath}`);
    }
  });
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
