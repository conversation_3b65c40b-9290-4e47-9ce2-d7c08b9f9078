{"name": "ecommerce-3d", "version": "0.1.0", "private": true, "license": "MIT", "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@mediapipe/tasks-vision": "^0.10.20", "@react-three/drei": "^9.121.4", "@react-three/fiber": "^8.17.14", "axios": "^1.7.9", "cra-template": "1.2.0", "cross-env": "^7.0.3", "express": "^4.21.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "serve": "^14.2.4", "three": "^0.172.0", "web-vitals": "^4.2.4"}, "scripts": {"start": "serve -s build -l $PORT", "dev": "react-scripts start", "build": "cross-env GENERATE_SOURCEMAP=false NODE_OPTIONS='--max_old_space_size=800' react-scripts build", "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "test": "react-scripts test", "eject": "react-scripts eject", "optimize-images": "node scripts/optimize-images.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}