import React, { useState, useEffect } from 'react';

const MobileTestComponent = () => {
  const [deviceInfo, setDeviceInfo] = useState({});
  const [touchTests, setTouchTests] = useState([]);
  const [responsiveTests, setResponsiveTests] = useState([]);

  useEffect(() => {
    // Gather device information
    const info = {
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio,
      isMobile: window.innerWidth <= 768,
      hasTouch: 'ontouchstart' in window,
      userAgent: navigator.userAgent,
      platform: navigator.platform
    };
    setDeviceInfo(info);

    // Run responsive tests
    runResponsiveTests();
    runTouchTests();
  }, []);

  const runResponsiveTests = () => {
    const tests = [];
    
    // Test viewport meta tag
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    tests.push({
      name: 'Viewport Meta Tag',
      passed: !!viewportMeta,
      details: viewportMeta ? viewportMeta.content : 'Not found'
    });

    // Test CSS media queries
    const isMobileCSS = window.matchMedia('(max-width: 768px)').matches;
    tests.push({
      name: 'Mobile CSS Media Query',
      passed: deviceInfo.isMobile === isMobileCSS,
      details: `CSS mobile: ${isMobileCSS}, JS mobile: ${deviceInfo.isMobile}`
    });

    // Test font sizes
    const testElement = document.createElement('div');
    testElement.style.fontSize = '16px';
    document.body.appendChild(testElement);
    const computedFontSize = window.getComputedStyle(testElement).fontSize;
    document.body.removeChild(testElement);
    
    tests.push({
      name: 'Minimum Font Size',
      passed: parseFloat(computedFontSize) >= 16,
      details: `Font size: ${computedFontSize}`
    });

    setResponsiveTests(tests);
  };

  const runTouchTests = () => {
    const tests = [];

    // Test touch support
    tests.push({
      name: 'Touch Events Support',
      passed: 'ontouchstart' in window,
      details: 'Touch events available'
    });

    // Test touch action CSS
    const canvas = document.querySelector('canvas');
    if (canvas) {
      const touchAction = window.getComputedStyle(canvas).touchAction;
      tests.push({
        name: 'Canvas Touch Action',
        passed: touchAction === 'manipulation',
        details: `Touch action: ${touchAction}`
      });
    }

    // Test button sizes
    const buttons = document.querySelectorAll('button, .nav-link');
    let minButtonSize = Infinity;
    let allButtonsValid = true;

    buttons.forEach(button => {
      const rect = button.getBoundingClientRect();
      const size = Math.min(rect.width, rect.height);
      if (size < minButtonSize) minButtonSize = size;
      if (size < 44) allButtonsValid = false;
    });

    tests.push({
      name: 'Touch Target Sizes',
      passed: allButtonsValid,
      details: `Minimum button size: ${minButtonSize.toFixed(1)}px (should be ≥44px)`
    });

    setTouchTests(tests);
  };

  const TestResult = ({ test }) => (
    <div style={{
      padding: '10px',
      margin: '5px 0',
      borderRadius: '5px',
      backgroundColor: test.passed ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)',
      border: `1px solid ${test.passed ? 'green' : 'red'}`
    }}>
      <div style={{ fontWeight: 'bold', color: test.passed ? 'green' : 'red' }}>
        {test.passed ? '✓' : '✗'} {test.name}
      </div>
      <div style={{ fontSize: '14px', color: '#666', marginTop: '5px' }}>
        {test.details}
      </div>
    </div>
  );

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '300px',
      maxHeight: '80vh',
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      fontSize: '12px',
      overflow: 'auto',
      zIndex: 10000,
      fontFamily: 'Arial, sans-serif'
    }}>
      <h3 style={{ margin: '0 0 15px 0', color: '#00ffcc' }}>Mobile Test Results</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#fff' }}>Device Info</h4>
        <div style={{ fontSize: '11px' }}>
          <div>Screen: {deviceInfo.screenWidth}×{deviceInfo.screenHeight}</div>
          <div>Viewport: {deviceInfo.viewportWidth}×{deviceInfo.viewportHeight}</div>
          <div>DPR: {deviceInfo.devicePixelRatio}</div>
          <div>Mobile: {deviceInfo.isMobile ? 'Yes' : 'No'}</div>
          <div>Touch: {deviceInfo.hasTouch ? 'Yes' : 'No'}</div>
        </div>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#fff' }}>Responsive Tests</h4>
        {responsiveTests.map((test, index) => (
          <TestResult key={index} test={test} />
        ))}
      </div>

      <div>
        <h4 style={{ margin: '0 0 10px 0', color: '#fff' }}>Touch Tests</h4>
        {touchTests.map((test, index) => (
          <TestResult key={index} test={test} />
        ))}
      </div>

      <div style={{ marginTop: '15px', textAlign: 'center' }}>
        <button
          onClick={() => {
            runResponsiveTests();
            runTouchTests();
          }}
          style={{
            backgroundColor: '#00ffcc',
            color: 'black',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          Refresh Tests
        </button>
      </div>
    </div>
  );
};

export default MobileTestComponent;
