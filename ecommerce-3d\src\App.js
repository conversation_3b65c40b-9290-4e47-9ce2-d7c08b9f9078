import React, { useState, useEffect, Suspense, lazy, memo } from 'react';
import { Routes, Route, Link, useLocation, Navigate, useNavigate } from 'react-router-dom';
import './App.css';
import { jwtDecode } from 'jwt-decode';

// Lazy load components for better performance
const Home = lazy(() => import('./Home'));
const About = lazy(() => import('./About'));
const ProductPage = lazy(() => import('./ProductPage'));
const ProductDetails = lazy(() => import('./ProductDetails'));
const Payment = lazy(() => import('./Payment'));
const LoginRegisterModal = lazy(() => import('./LoginRegisterModal'));
const Cart = lazy(() => import('./Cart'));
const PaymentHistory = lazy(() => import('./PaymentHistory'));

// Loading component
const LoadingSpinner = memo(() => (
  <div className="loading-container">
    <div className="socket">
      <div className="gel center-gel">
        <div className="hex-brick h1"></div>
        <div className="hex-brick h2"></div>
        <div className="hex-brick h3"></div>
      </div>
    </div>
  </div>
));

const App = memo(() => {
  const location = useLocation();
  const navigate = useNavigate();
  const [pageTitle, setPageTitle] = useState('');
  const [authModal, setAuthModal] = useState({ isOpen: false, isLogin: true });
  const [isLoggedIn, setIsLoggedIn] = useState(!!localStorage.getItem('token'));
  const [isAdmin, setIsAdmin] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [triggerDelete, setTriggerDelete] = useState(false);
  const [username, setUsername] = useState('');
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [isFloatingWindowVisible, setIsFloatingWindowVisible] = useState(false);
  const [cartItemCount, setCartItemCount] = useState(0);

  // Register service worker for caching and performance
  useEffect(() => {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('✅ SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('❌ SW registration failed: ', registrationError);
          });
      });
    }
  }, []);

  useEffect(() => {
    if (location.pathname === '/') {
      setPageTitle('The most Hottest items for this week');
    } else if (location.pathname === '/about') {
      setPageTitle('Learn more about us');
    } else if (location.pathname === '/products') {
      setPageTitle('Discover Our Product Line');
    } else if (location.pathname === '/cart') {
      setPageTitle('Your Cart');
    } else if (location.pathname === '/payment-history') {
      setPageTitle('Payment History');
    } else {
      setPageTitle('');
    }
  }, [location]);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const decoded = jwtDecode(token);
        console.log('Decoded Token:', decoded);
        setIsAdmin(decoded.isAdmin || false);
        setUsername(decoded.username || decoded.name || decoded.email || decoded.sub || 'Guest');
      } catch (err) {
        console.error('Token decode error:', err);
        setIsAdmin(false);
        setUsername('Guest');
      }
    } else {
      setIsAdmin(false);
      setUsername('');
    }
  }, [isLoggedIn]);

  // Update cart item count
  useEffect(() => {
    const updateCartCount = () => {
      const cart = JSON.parse(localStorage.getItem('cart')) || [];
      const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
      setCartItemCount(totalItems);
    };

    updateCartCount();

    // Listen for storage changes to update cart count when items are added
    const handleStorageChange = (e) => {
      if (e.key === 'cart') {
        updateCartCount();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom cart update events
    const handleCartUpdate = () => updateCartCount();
    window.addEventListener('cartUpdated', handleCartUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, []);

  const handleLoginStateChange = () => {
    setIsLoggedIn(!!localStorage.getItem('token'));
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    setIsLoggedIn(false);
    setIsAdmin(false);
    setUsername('');
  };

  const toggleMobileNav = () => {
    setIsMobileNavOpen(!isMobileNavOpen);
  };

  const toggleFloatingWindow = () => {
    setIsFloatingWindowVisible(!isFloatingWindowVisible);
  };

  const handleBackClick = () => {
    navigate(-1); // Go back to previous page
  };

  // Check if we should show back arrow (all pages except home)
  const shouldShowBackArrow = location.pathname !== '/';

  return (
    <div>
      <nav className="navbar">
        {/* Back Arrow - show on all pages except home */}
        {shouldShowBackArrow && (
          <button
            className="back-arrow-btn"
            onClick={handleBackClick}
            aria-label="Go back"
          >
            <span className="back-arrow">←</span>
          </button>
        )}

        <button
          className={`mobile-nav-toggle ${isMobileNavOpen ? 'active' : ''}`}
          onClick={toggleMobileNav}
          aria-label="Toggle mobile menu"
        >
          <span></span>
          <span></span>
          <span></span>
          {/* Mobile Cart Badge on Toggle Button */}
          {cartItemCount > 0 && (
            <span className="mobile-toggle-cart-badge">{cartItemCount}</span>
          )}
        </button>

        {/* Mobile Page Title - centered in navbar - only show on home page */}
        <div className={`mobile-page-title ${location.pathname === '/' ? 'show-on-home' : ''}`}>
          {location.pathname === '/' ? pageTitle : ''}
        </div>

        <div className="nav-center">
          <Link to="/" className="nav-link">Hot Items</Link>
          <Link to="/about" className="nav-link">About</Link>
          <Link to="/products" className="nav-link">All Products</Link>
        </div>
        <div className="nav-right">
          {/* Cart link - visible to all users */}
          <Link to="/cart" className="nav-link cart-link">
            🛒 Cart
            {cartItemCount > 0 && (
              <span className="cart-badge">{cartItemCount}</span>
            )}
          </Link>

          {isLoggedIn ? (
            <>
              {isAdmin && location.pathname === '/products' && (
                <>
                  <button className="nav-link" onClick={() => setShowAddForm(true)}>
                    Add New Product
                  </button>
                  <button className="nav-link" onClick={() => setTriggerDelete(true)}>
                    Delete
                  </button>
                </>
              )}
              <button className="nav-link" onClick={handleLogout}>
                Logout
              </button>
            </>
          ) : (
            <>
              <button
                className="nav-link"
                onClick={() => setAuthModal({ isOpen: true, isLogin: true })}
              >
                Login
              </button>
              <button
                className="nav-link"
                onClick={() => setAuthModal({ isOpen: true, isLogin: false })}
              >
                Register
              </button>
            </>
          )}
        </div>
      </nav>

      {/* Mobile Navigation Overlay */}
      <div
        className={`mobile-nav-overlay ${isMobileNavOpen ? 'active' : ''}`}
        onClick={toggleMobileNav}
      ></div>

      {/* Mobile Navigation Menu */}
      <div className={`mobile-nav ${isMobileNavOpen ? 'active' : ''}`}>
        <Link to="/" className="nav-link" onClick={toggleMobileNav}>
          🏠 Hot Items
        </Link>
        <Link to="/about" className="nav-link" onClick={toggleMobileNav}>
          ℹ️ About
        </Link>
        <Link to="/products" className="nav-link" onClick={toggleMobileNav}>
          📱 All Products
        </Link>

        {/* Cart link - visible to all users in mobile nav */}
        <Link to="/cart" className="nav-link cart-link" onClick={toggleMobileNav}>
          🛒 Cart
          {cartItemCount > 0 && (
            <span className="cart-badge">{cartItemCount}</span>
          )}
        </Link>

        {isLoggedIn ? (
          <>
            {isAdmin && (
              <>
                <Link to="/payment-history" className="nav-link" onClick={toggleMobileNav}>
                  📊 Payment History
                </Link>
                {location.pathname === '/products' && (
                  <>
                    <button className="nav-link" onClick={() => {
                      setShowAddForm(true);
                      toggleMobileNav();
                    }}>
                      ➕ Add Product
                    </button>
                    <button className="nav-link" onClick={() => {
                      setTriggerDelete(true);
                      toggleMobileNav();
                    }}>
                      🗑️ Delete Product
                    </button>
                  </>
                )}
              </>
            )}
            <button className="nav-link" onClick={() => {
              handleLogout();
              toggleMobileNav();
            }}>
              🚪 Logout
            </button>
          </>
        ) : (
          <>
            <button
              className="nav-link"
              onClick={() => {
                setAuthModal({ isOpen: true, isLogin: true });
                toggleMobileNav();
              }}
            >
              🔑 Login
            </button>
            <button
              className="nav-link"
              onClick={() => {
                setAuthModal({ isOpen: true, isLogin: false });
                toggleMobileNav();
              }}
            >
              📝 Register
            </button>
          </>
        )}
      </div>

      <div
        className={`pageTitle ${location.pathname === '/' ? 'hide-on-mobile-home' : ''} ${location.pathname === '/about' || location.pathname === '/products' ? 'hide-page-title' : ''}`}
        style={{
          display: location.pathname === '/payment' || location.pathname === '/cart' || location.pathname === '/about' || location.pathname === '/products' ? 'none' : 'block'
        }}
      >
        {pageTitle}
      </div>

      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/products" element={<ProductPage isAdmin={isAdmin} showAddForm={showAddForm} setShowAddForm={setShowAddForm} triggerDelete={triggerDelete} setTriggerDelete={setTriggerDelete} />} />
          <Route path="/product/:id" element={<ProductDetails />} />
          <Route path="/payment" element={<Payment isAdmin={isAdmin} />} />
          <Route path="/cart" element={<Cart />} />
          <Route
            path="/payment-history"
            element={isAdmin ? <PaymentHistory /> : <Navigate to="/" />}
          />
        </Routes>
      </Suspense>

      {authModal.isOpen && (
        <Suspense fallback={<LoadingSpinner />}>
          <LoginRegisterModal
            isLogin={authModal.isLogin}
            onClose={() => setAuthModal({ isOpen: false, isLogin: true })}
            onLoginStateChange={handleLoginStateChange}
          />
        </Suspense>
      )}

      <footer>
        {isLoggedIn && (
          <span className="username-footer">User: {username || 'Guest'}</span>
        )}
        <p>Produced by Chouchane Med Amine | All Rights is Reserved {new Date().getFullYear()}</p>
        <button className="floating-window-toggle" onClick={toggleFloatingWindow}>
          {isFloatingWindowVisible ? 'Hide Featured' : 'Show Featured'}
        </button>
      </footer>
    </div>
  );
});

App.displayName = 'App';

export default App;