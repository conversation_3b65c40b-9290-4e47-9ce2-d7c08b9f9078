import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import './App.css';

const Cart = () => {
  const [cart, setCart] = useState([]);
  const [total, setTotal] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  // Debug function to analyze cart data
  const debugCartData = (cartData, label) => {
    console.log(`=== ${label} ===`);
    console.log('Total items:', cartData.length);

    const idMap = new Map();
    cartData.forEach((item, index) => {
      const itemId = item._id || item.id;
      console.log(`Item ${index}:`, {
        id: itemId,
        _id: item._id,
        id_field: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity
      });

      if (idMap.has(itemId)) {
        console.warn(`⚠️ DUPLICATE ID DETECTED: ${itemId}`, {
          existing: idMap.get(itemId),
          duplicate: item
        });
      } else {
        idMap.set(itemId, item);
      }
    });
    console.log('=================');
  };

  useEffect(() => {
    const savedCart = JSON.parse(localStorage.getItem('cart')) || [];
    debugCartData(savedCart, 'LOADED CART DATA'); // Debug cart data

    // Validate and clean cart data
    const validatedCart = savedCart.filter(item => {
      const itemId = item._id || item.id;
      if (!itemId) {
        console.warn('Cart item missing ID, removing:', item);
        return false;
      }
      return true;
    });

    // Remove duplicates based on ID
    const uniqueCart = validatedCart.reduce((acc, current) => {
      const currentId = current._id || current.id;
      const existingIndex = acc.findIndex(item => {
        const existingId = item._id || item.id;
        return existingId === currentId;
      });

      if (existingIndex >= 0) {
        // Merge quantities if duplicate found
        acc[existingIndex].quantity += current.quantity || 1;
        console.log(`Merged duplicate cart item with ID ${currentId}`);
      } else {
        acc.push(current);
      }
      return acc;
    }, []);

    debugCartData(uniqueCart, 'CLEANED CART DATA');

    setCart(uniqueCart);
    calculateTotal(uniqueCart);

    // Update localStorage with cleaned cart
    if (uniqueCart.length !== savedCart.length) {
      localStorage.setItem('cart', JSON.stringify(uniqueCart));
      console.log('Cart cleaned and updated in localStorage');
    }

    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const calculateTotal = (items) => {
    const totalPrice = items.reduce((sum, item) => {
      const price = typeof item.price === 'string' ? parseFloat(item.price.replace('$', '')) : item.price;
      return sum + (price * item.quantity);
    }, 0);
    setTotal(totalPrice.toFixed(2));
  };

  const updateQuantity = (id, quantity) => {
    const updatedCart = cart.map(item =>
      (item._id === id || item.id === id) ? { ...item, quantity: Math.max(0, quantity) } : item
    ).filter(item => item.quantity > 0);

    setCart(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
    calculateTotal(updatedCart);

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const removeItem = (id) => {
    const updatedCart = cart.filter(item => item._id !== id && item.id !== id);
    setCart(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
    calculateTotal(updatedCart);

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const clearCart = () => {
    setCart([]);
    localStorage.removeItem('cart');
    setTotal(0);

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));
  };

  return (
    <div className="cart-container">
      <div className="cart-header">
        <h1>🛒 Shopping Cart</h1>
        <p>Review and manage your selected items</p>
      </div>

      {cart.length === 0 ? (
        <p style={{ textDecoration: 'underline', color: '#fff', textShadow: '2px 2px 5px rgba(0, 0, 0, 0.7)' }}>
          Your cart is currently empty
        </p>
      ) : (
        <>
          <div className="cart-items">
            {cart.map(item => {
              const itemId = item._id || item.id;
              const itemImage = item.image || '/uploads/iPhone-16-Pro-& pro maxlarge.png';

              console.log('Rendering cart item:', {
                id: itemId,
                name: item.name,
                image: itemImage,
                price: item.price,
                quantity: item.quantity,
                hasId: !!item.id,
                has_id: !!item._id,
                originalItem: item
              });

              // Create a unique key that includes both ID and name to prevent conflicts
              const uniqueKey = `${itemId}-${item.name.replace(/\s+/g, '-').toLowerCase()}`;

              return (
                <div key={uniqueKey} className="cart-item">
                  <img
                    src={itemImage}
                    alt={item.name}
                    className="cart-item-image"
                    onError={(e) => {
                      console.log('Image failed to load:', itemImage);
                      e.target.src = '/uploads/iPhone-16-Pro-& pro maxlarge.png';
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', itemImage);
                    }}
                  />
                  <div className="cart-item-details">
                    <h3>{item.name}</h3>
                    <p className="cart-item-price">
                      ${typeof item.price === 'string' ? parseFloat(item.price.replace('$', '')) : item.price}
                    </p>
                    {item.description && (
                      <p className="cart-item-description">{item.description}</p>
                    )}
                    <div className="quantity-controls">
                      <button
                        onClick={() => updateQuantity(itemId, item.quantity - 1)}
                      >
                        -
                      </button>
                      <span>Qty: {item.quantity}</span>
                      <button
                        onClick={() => updateQuantity(itemId, item.quantity + 1)}
                      >
                        +
                      </button>
                    </div>
                    <button
                      className="remove-item"
                      onClick={() => removeItem(itemId)}
                    >
                      Remove
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="cart-summary">
            <h3>Total: ${total}</h3>
            <div className="cart-actions">
              <button
                className="clear-cart"
                onClick={clearCart}
              >
                Clear Cart
              </button>
              <Link to="/payment" state={{ cart, total }} className="checkout-button">
                Proceed to Checkout
              </Link>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Cart;