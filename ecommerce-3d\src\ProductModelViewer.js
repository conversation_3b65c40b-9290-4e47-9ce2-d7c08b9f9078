import React from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";

const ProductModelViewer = ({ modelPath }) => {
  const [model, setModel] = React.useState(null);
  const [error, setError] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    if (!modelPath) {
      setError(true);
      setLoading(false);
      return;
    }

    const loader = new GLTFLoader();
    setLoading(true);
    setError(false);

    loader.load(
      modelPath,
      (gltf) => {
        setModel(gltf.scene);
        setLoading(false);
      },
      (progress) => {
        // Handle loading progress with safety check
        if (progress.total && progress.total > 0) {
          const percentage = Math.round((progress.loaded / progress.total) * 100);
          console.log('Loading progress:', percentage + '%');
        } else {
          console.log('Loading progress: calculating...');
        }
      },
      (error) => {
        console.error("Error loading model:", error);
        setError(true);
        setLoading(false);
      }
    );
  }, [modelPath]);

  if (loading) {
    return (
      <div style={{ width: "100%", height: "300px", display: "flex", alignItems: "center", justifyContent: "center" }}>
        Loading 3D Model...
      </div>
    );
  }

  if (error || !model) {
    return (
      <Canvas style={{ width: "100%", height: "300px" }}>
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} />
        {/* Fallback cube */}
        <mesh>
          <boxGeometry args={[2, 2, 2]} />
          <meshStandardMaterial color="#666" />
        </mesh>
        <OrbitControls />
        <Environment preset="sunset" />
      </Canvas>
    );
  }

  return (
    <Canvas style={{ width: "100%", height: "300px" }}>
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} />
      <primitive object={model} />
      <OrbitControls />
      <Environment preset="sunset" />
    </Canvas>
  );
};

export default ProductModelViewer;
