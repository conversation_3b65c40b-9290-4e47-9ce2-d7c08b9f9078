const express = require('express');
const path = require('path');
const app = express();

const port = process.env.PORT || 10000;

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'build')));

// API routes would go here if needed
// app.use('/api', apiRoutes);

// Catch all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(port, '0.0.0.0', () => {
  console.log(`Server is running on port ${port}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
  console.log(`API URL: ${process.env.REACT_APP_API_URL}`);
});
