import React, { useState, useEffect } from 'react';
import './App.css';

const PaymentHistory = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch payment history from localStorage
    const paymentHistory = JSON.parse(localStorage.getItem('paymentHistory')) || [];
    setPayments(paymentHistory);
    setLoading(false);
  }, []);

  if (loading) return <div className="loading">Loading payment history...</div>;

  return (
    <div className="payment-history-container">
      <h2>Payment History</h2>
      {payments.length === 0 ? (
        <p style={{ textDecoration: 'underline', color: '#fff', textShadow: '2px 2px 5px rgba(0, 0, 0, 0.7)' }}>
          No payment history available
        </p>
      ) : (
        <div className="payment-history-list">
          {payments.map(payment => (
            <div key={payment.id} className="payment-history-item">
              <p><strong>User:</strong> {payment.user} ({payment.email})</p>
              <p><strong>Items:</strong> {payment.items}</p>
              <p><strong>Total:</strong> ${payment.total.toFixed(2)}</p>
              <p><strong>Date:</strong> {payment.date}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentHistory;