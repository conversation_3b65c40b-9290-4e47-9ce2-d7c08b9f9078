/* ===== HOME CONTAINER ===== */
.home-container {
  position: relative;
  min-height: 100vh;
  width: 100vw;
  overflow-x: hidden; /* Allow vertical scroll, hide horizontal */
  /* Background removed to show wallpaper */
}

.home-container.mobile {
  overflow-x: hidden;
}

.home-container.tablet {
  overflow-x: hidden;
}

/* Hero section styles removed as requested */

/* ===== BACKGROUND CONTENT ===== */
.background-content {
  position: relative;
  height: 100vh;
  width: 100%;
  transition: filter 0.3s ease;
}

.background-content.blurred {
  filter: blur(5px);
  pointer-events: none;
}

/* ===== SWIPE INDICATOR - HALF CIRCLE DESIGN ===== */
.swipe-indicator {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 255, 204, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(0, 255, 204, 0.3);
  border-right: none;
  border-radius: 50px 0 0 50px;
  padding: 12px 8px 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 500;
  animation: pulseGlow 3s ease-in-out infinite;
  transition: all 0.3s ease;
  width: 120px;
  height: 60px;
  justify-content: flex-start;
}

.swipe-indicator.fade-out {
  opacity: 0.2;
  transform: translateY(-50%) scale(0.95);
  pointer-events: none;
  animation: none;
}

.swipe-arrow {
  font-size: 1.2rem;
  color: #00ffcc;
  animation: slideLeft 2.5s ease-in-out infinite;
  margin-left: 4px;
}

.swipe-text {
  color: #00ffcc;
  font-size: 0.7rem;
  font-weight: 500;
  text-shadow: 0 2px 8px rgba(0, 255, 204, 0.3);
  white-space: nowrap;
  text-align: center;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: -5px 0 15px rgba(0, 255, 204, 0.2), 0 0 10px rgba(0, 255, 204, 0.1);
  }
  50% {
    box-shadow: -8px 0 25px rgba(0, 255, 204, 0.4), 0 0 20px rgba(0, 255, 204, 0.2);
  }
}

@keyframes slideLeft {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-4px);
  }
}

/* ===== FLOATING PRODUCT WINDOW ===== */
.floating-product-window {
  position: fixed;
  right: -100%;
  top: 50%;
  transform: translateY(-50%);
  width: 380px;
  max-width: 90vw;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.5);
  transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1000;
  overflow: hidden;
}

.floating-product-window.visible {
  right: 30px;
}

.floating-product-window.hidden {
  right: -100%;
}

.floating-product-window.tablet-size {
  width: 320px;
}

.floating-product-window.tablet-size.visible {
  right: 20px;
}

.product-window-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.featured-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.product-image-container {
  padding: 20px;
  text-align: center;
}

.product-window-img {
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.product-window-img:hover {
  transform: scale(1.05);
}

.product-window-content {
  padding: 0 20px 25px;
  color: white;
}

.product-window-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  text-align: center;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

/* ===== PRICE STYLING ===== */
.price-container {
  text-align: center;
  margin-bottom: 20px;
}

.price-with-discount {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.original-price {
  color: #ff6b6b;
  text-decoration: line-through;
  font-size: 1rem;
  opacity: 0.8;
}

.discounted-price {
  color: #00ff88;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 15px rgba(0, 255, 136, 0.4);
}

.discount-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.regular-price {
  color: #00ff88;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 15px rgba(0, 255, 136, 0.4);
}

.product-window-description {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  text-align: center;
  margin-bottom: 25px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== PRODUCT ACTIONS ===== */
.product-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.add-to-cart-btn {
  background: linear-gradient(135deg, #00ffcc 0%, #00ccaa 100%);
  color: #000;
  border: none;
  padding: 15px 25px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 255, 204, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 255, 204, 0.5);
  background: linear-gradient(135deg, #00ccaa 0%, #00ffcc 100%);
}

.add-to-cart-btn:active {
  transform: translateY(0);
}

.btn-icon {
  font-size: 1.1rem;
}

.view-details-btn {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 25px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.view-details-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes slideInRight {
  from {
    right: -100%;
  }
  to {
    right: 30px;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-section {
    top: 12%;
    max-width: 600px;
  }

  .hero-content {
    padding: 30px 25px;
  }

  .floating-product-window {
    width: 320px;
  }

  .floating-product-window.visible {
    right: 20px;
  }
}

@media (max-width: 768px) {
  /* Smaller half-circle swipe indicator for mobile */
  .swipe-indicator {
    right: 0;
    padding: 10px 6px 10px 12px;
    gap: 6px;
    width: 100px;
    height: 50px;
    border-radius: 40px 0 0 40px;
  }

  .swipe-arrow {
    font-size: 1rem;
    margin-left: 2px;
  }

  .swipe-text {
    font-size: 0.65rem;
  }

  .hero-section {
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    max-width: calc(100vw - 40px);
    padding: 0 20px;
  }

  .hero-content {
    padding: 25px 20px;
    border-radius: 15px;
  }

  .hero-title {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
    margin-bottom: 10px;
  }

  .hero-subtitle {
    font-size: clamp(1rem, 4vw, 1.4rem);
  }

  .hero-description {
    font-size: clamp(0.9rem, 3vw, 1.1rem);
    margin-bottom: 20px;
  }

  .cta-button {
    padding: 15px 30px;
    font-size: 1rem;
  }

  .floating-product-window {
    width: calc(100vw - 40px);
    max-width: 350px;
    right: -100%;
    top: 50%;
    transform: translateY(-50%);
  }

  .floating-product-window.visible {
    right: 20px;
  }

  .floating-product-window.hidden {
    right: -100%;
  }

  .product-window-content {
    padding: 0 15px 20px;
  }

  .product-window-title {
    font-size: 1.2rem;
  }

  .discounted-price,
  .regular-price {
    font-size: 1.5rem;
  }

  .product-actions {
    gap: 10px;
  }

  .add-to-cart-btn {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .view-details-btn {
    padding: 10px 20px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  /* Ultra-compact half-circle for small mobile screens */
  .swipe-indicator {
    right: 0;
    padding: 8px 4px 8px 10px;
    gap: 4px;
    width: 80px;
    height: 40px;
    border-radius: 30px 0 0 30px;
  }

  .swipe-arrow {
    font-size: 0.9rem;
    margin-left: 1px;
  }

  .swipe-text {
    font-size: 0.6rem;
  }

  .hero-section {
    top: 18%;
    padding: 0 15px;
  }

  .hero-content {
    padding: 20px 15px;
  }

  .floating-product-window {
    width: calc(100vw - 20px);
    max-width: none;
  }

  .floating-product-window.visible {
    right: 10px;
  }

  .product-image-container {
    padding: 15px;
  }

  .product-window-img {
    max-width: 150px;
  }

  .product-window-content {
    padding: 0 10px 15px;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .hero-content {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #ffffff;
  }

  .floating-product-window {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffffff;
  }

  .add-to-cart-btn {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
  }

  .view-details-btn {
    border: 2px solid #ffffff;
    background: transparent;
  }
}

/* ===== ENHANCED MODAL STYLES ===== */
.enhanced-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.enhanced-product-modal {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 4px 15px rgba(91, 3, 244, 0.3);
  width: 90%;
  max-width: 900px;
  height: 85vh;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  position: relative;
  animation: slideInScale 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.enhanced-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.enhanced-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.modal-image-section {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.02);
}

.enhanced-modal-image {
  width: 100%;
  max-width: 350px;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease;
}

.enhanced-modal-image:hover {
  transform: scale(1.05);
}

.modal-content-section {
  flex: 1;
  padding: 40px;
  color: white;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.modal-content-section::-webkit-scrollbar {
  display: none; /* WebKit */
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 20px;
}

.modal-product-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

.modal-price-container {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.modal-price-with-discount {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.modal-original-price {
  color: #ff6b6b;
  text-decoration: line-through;
  font-size: 1.2rem;
  opacity: 0.8;
}

.modal-discounted-price {
  color: #00ff88;
  font-size: 2.2rem;
  font-weight: 700;
  text-shadow: 0 2px 15px rgba(0, 255, 136, 0.4);
}

.modal-discount-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-regular-price {
  color: #00ff88;
  font-size: 2.2rem;
  font-weight: 700;
  text-shadow: 0 2px 15px rgba(0, 255, 136, 0.4);
}

.modal-specs-section {
  background: rgba(255, 255, 255, 0.03);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #00ffcc;
  margin-bottom: 15px;
  text-shadow: 0 2px 10px rgba(0, 255, 204, 0.3);
}

.enhanced-specs-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.enhanced-spec-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(0, 255, 204, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.1);
  transition: all 0.3s ease;
}

.enhanced-spec-item:hover {
  background: rgba(0, 255, 204, 0.1);
  border-color: rgba(0, 255, 204, 0.3);
}

.spec-icon {
  color: #00ff88;
  font-weight: bold;
  font-size: 1.1rem;
}

.spec-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.modal-description-section {
  background: rgba(255, 255, 255, 0.03);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.description-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #00ffcc;
  margin-bottom: 15px;
  text-shadow: 0 2px 10px rgba(0, 255, 204, 0.3);
}

.enhanced-modal-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.modal-actions {
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.enhanced-add-to-cart-btn {
  background: linear-gradient(135deg, #00ffcc 0%, #00ccaa 100%);
  color: #000;
  border: none;
  padding: 18px 40px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 255, 204, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  width: 100%;
}

.enhanced-add-to-cart-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(0, 255, 204, 0.5);
  background: linear-gradient(135deg, #00ccaa 0%, #00ffcc 100%);
}

/* ===== MODAL ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ===== MODAL RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .enhanced-product-modal {
    flex-direction: column;
    width: 95%;
    height: 90vh;
    max-height: 90vh;
    margin: 20px;
    overflow: hidden;
  }

  .modal-image-section {
    padding: 15px;
    flex: none;
    max-height: 35%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .enhanced-modal-image {
    max-width: 200px;
    max-height: 100%;
    object-fit: contain;
  }

  .modal-content-section {
    padding: 15px;
    gap: 15px;
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .modal-content-section::-webkit-scrollbar {
    display: none;
  }

  .modal-product-title {
    font-size: 1.5rem;
  }

  .modal-discounted-price,
  .modal-regular-price {
    font-size: 1.8rem;
  }

  .enhanced-specs-list {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .enhanced-spec-item {
    padding: 8px;
  }

  .enhanced-add-to-cart-btn {
    padding: 15px 30px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .enhanced-product-modal {
    width: 98%;
    height: 95vh;
    max-height: 95vh;
    margin: 10px;
    border-radius: 15px;
    overflow: hidden;
  }

  .modal-image-section {
    padding: 10px;
    max-height: 30%;
  }

  .enhanced-modal-image {
    max-width: 150px;
    max-height: 100%;
    object-fit: contain;
  }

  .modal-content-section {
    padding: 10px;
    gap: 10px;
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .modal-content-section::-webkit-scrollbar {
    display: none;
  }

  .modal-product-title {
    font-size: 1.3rem;
  }

  .modal-price-with-discount {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .modal-discounted-price,
  .modal-regular-price {
    font-size: 1.5rem;
  }

  .specs-title,
  .description-title {
    font-size: 1rem;
  }

  .enhanced-add-to-cart-btn {
    padding: 12px 25px;
    font-size: 0.9rem;
  }
}

/* Model Container */
.model-container {
  width: 100%;
  height: 100vh;
  position: relative;
  z-index: 1;
}

/* 3D Model Size Controls */
.model-size-controls {
  position: fixed;
  bottom: 80px; /* Above footer */
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.model-size-controls:hover {
  opacity: 1;
}

.size-control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: white;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(168, 86, 245, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.size-control-btn:hover {
  background: rgba(168, 86, 245, 0.8);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(168, 86, 245, 0.5);
  border-color: rgba(255, 255, 255, 0.3);
}

.size-control-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.size-reset {
  font-size: 18px;
}

/* Mobile responsive adjustments for size controls */
@media (max-width: 768px) {
  .model-size-controls {
    bottom: 70px;
    left: 15px;
    gap: 8px;
  }

  .size-control-btn {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }

  .size-reset {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .model-size-controls {
    bottom: 65px;
    left: 10px;
    gap: 6px;
  }

  .size-control-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .size-reset {
    font-size: 14px;
  }
}