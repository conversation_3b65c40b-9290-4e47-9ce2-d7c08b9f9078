import React from "react";
import ReactDOM from "react-dom/client"; // Import from 'react-dom/client' for React 18+
import "./index.css";
import App from "./App";
import { BrowserRouter as Router } from "react-router-dom"; // Import Router

const root = document.getElementById("root");

if (root) {
  const reactRoot = ReactDOM.createRoot(root); // Use React 18 method
  reactRoot.render(
    <Router>
      <App />
    </Router>
  );
} else {
  console.error("Root container not found!");
}
