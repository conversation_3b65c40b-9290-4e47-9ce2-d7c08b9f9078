import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import api from './api/axios';
import './App.css';

// Fallback products for when API is not available - CORRECTED MAPPING
const fallbackProducts = [
  {
    id: 1,
    name: "iPhone 16 Pro Max", // Matches iphone_16_pro_max.glb
    image: "/uploads/iPhone-16-Pro-& pro maxlarge.png",
    price: "$1,199",
    description: "Latest iPhone with advanced camera system",
    specs: ["6.7-inch display", "A18 Pro chip", "Pro camera system"]
  },
  {
    id: 2,
    name: "Samsung Galaxy S24 Ultra", // Matches samsung_s24_ultra.glb
    image: "/uploads/s24 ultra.PNG",
    price: "$1,299",
    description: "Premium Android smartphone",
    specs: ["6.8-inch display", "S Pen included", "200MP camera"]
  },
  {
    id: 3,
    name: "MacBook Pro", // Matches apple-macbook-pro.glb
    image: "/uploads/Apple-Macbook-Pro-Transparent-Image.png",
    price: "$2,499",
    description: "Powerful laptop for professionals",
    specs: ["M3 Pro chip", "16-inch display", "32GB RAM"]
  },
  {
    id: 4,
    name: "PlayStation 5", // Matches playstation_5.glb
    image: "/uploads/Sony PlayStation 5.jpg",
    price: "$499",
    description: "Next-generation gaming console",
    specs: ["4K gaming", "Ray tracing", "SSD storage"]
  },
  {
    id: 5,
    name: "Apple Vision Pro", // No GLB file available
    image: "/uploads/Apple-WWDC23-Vision-Pro-glass-230605_big.jpg.large.png",
    price: "$3,499",
    description: "Revolutionary mixed reality headset",
    specs: ["Spatial computing", "Eye tracking", "Hand tracking"]
  },
  {
    id: 6,
    name: "Apple Watch Series 6", // Matches apple_watch_series_6.glb
    image: "/uploads/Apple Watch Series 7.jpg",
    price: "$399",
    description: "Advanced smartwatch with health monitoring",
    specs: ["GPS + Cellular", "Blood oxygen monitoring", "ECG app"]
  },
  {
    id: 7,
    name: "RGS VR Headset", // Matches RGS_VR_headset.glb
    image: "/uploads/RGS_VR_headset.png",
    price: "$599",
    description: "Immersive VR gaming experience",
    specs: ["4K per eye", "120Hz refresh rate", "Wireless"]
  },
  {
    id: 8,
    name: "DJI FPV Drone", // No GLB file available
    image: "/uploads/GoPro Hero 9.jpg", // Using GoPro as placeholder
    price: "$1,299",
    description: "High-speed FPV racing drone",
    specs: ["4K video", "150km/h speed", "Emergency brake"]
  },
  {
    id: 9,
    name: "Xbox Series X", // No GLB file available
    image: "/uploads/pngimg.com - xbox_PNG101375.png",
    price: "$499",
    description: "Most powerful Xbox console",
    specs: ["4K gaming", "Quick Resume", "Smart Delivery"]
  },
  {
    id: 10,
    name: "Logitech G502 Lightspeed", // Has GLTF file in folder
    image: "/uploads/Logitech G502 Lightspeed.png",
    price: "$149",
    description: "Wireless gaming mouse",
    specs: ["25K DPI sensor", "Wireless", "RGB lighting"]
  },
  {
    id: 11,
    name: "MSI Gaming Headset", // No GLB file available
    image: "/uploads/msi headset.png",
    price: "$199",
    description: "Professional gaming headset",
    specs: ["7.1 surround sound", "Noise cancelling", "RGB lighting"]
  },
  {
    id: 12,
    name: "MSI Gaming Laptop", // Matches Msi LapTop.glb
    image: "/uploads/msi NBG.png",
    price: "$1,899",
    description: "High-performance gaming laptop",
    specs: ["RTX 4070", "Intel i7", "16GB RAM"]
  },
  {
    id: 13,
    name: "Samsung Galaxy AirPods", // Matches samsung_galaxy_AirPods.glb
    image: "/uploads/Samsung airpods.png",
    price: "$199",
    description: "Wireless earbuds with ANC",
    specs: ["Active noise cancelling", "Wireless charging", "8-hour battery"]
  },
  {
    id: 14,
    name: "iPhone 14 Pro Max", // Matches iphone_14_pro_max.glb
    image: "/uploads/iPhone-14-Pro-Max.png",
    price: "$1,099",
    description: "Previous generation iPhone Pro",
    specs: ["6.7-inch display", "A16 Bionic chip", "Pro camera system"]
  },
  {
    id: 15,
    name: "ASUS ROG Strix Scar 17", // No GLB file available
    image: "/uploads/Asus ROG Strix Scar 17.png",
    price: "$2,299",
    description: "Ultimate gaming laptop",
    specs: ["RTX 4080", "AMD Ryzen 9", "32GB RAM"]
  }
];

const ProductPage = ({ isAdmin, showAddForm, setShowAddForm, triggerDelete, setTriggerDelete }) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [addFormData, setAddFormData] = useState({
    id: '',
    name: '',
    image: null,
    price: '',
    description: '',
    specs: ''
  });
  const [addError, setAddError] = useState('');
  const [imagePreview, setImagePreview] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await api.get('/products');
        const fetchedProducts = response.data.products || response.data;
        console.log('Fetched products:', fetchedProducts);
        setProducts(fetchedProducts);
      } catch (err) {
        console.warn('API not available, using fallback products:', err.message);
        // Use fallback products when API is not available
        setProducts(fallbackProducts);
        setError(''); // Clear error since we have fallback data
      } finally {
        setLoading(false);
      }
    };
    fetchProducts();
  }, []);

  const handleDelete = useCallback(async () => {
    if (!selectedProductId) {
      alert('Please select a product to delete');
      return;
    }
    if (!window.confirm('Are you sure you want to delete this product?')) return;

    try {
      await api.delete(`/products/delete/${selectedProductId}`);
      setProducts(products.filter((p) => p.id !== selectedProductId));
      setSelectedProductId(null);
      alert('Product deleted successfully');
    } catch (err) {
      console.error('Delete error:', err);
      alert('Failed to delete product');
    }
  }, [selectedProductId, products, setSelectedProductId]);

  useEffect(() => {
    if (triggerDelete) {
      handleDelete();
      setTriggerDelete(false);
    }
  }, [triggerDelete, handleDelete, setTriggerDelete]);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAddFormData({ ...addFormData, image: file });
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const handleAddSubmit = async (e) => {
    e.preventDefault();
    try {
      const formData = new FormData();
      formData.append('id', addFormData.id);
      formData.append('name', addFormData.name);
      formData.append('image', addFormData.image);
      formData.append('price', addFormData.price);
      formData.append('description', addFormData.description);
      formData.append('specs', addFormData.specs);

      console.log('Sending FormData:');
      for (let pair of formData.entries()) {
        console.log(`${pair[0]}: ${pair[1]}`);
      }

      const response = await api.post('/products/add', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      console.log('Added product:', response.data);
      setProducts(prevProducts => [...prevProducts, response.data]);
      setShowAddForm(false);
      setAddFormData({ id: '', name: '', image: null, price: '', description: '', specs: '' });
      setImagePreview(null);
      alert('Product added successfully');
    } catch (err) {
      setAddError(err.response?.data?.message || 'Failed to add product');
      console.error('Add error:', err);
    }
  };

  if (loading) return <div className="loading">Loading products...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <>
      {/* Fixed title for All Products page */}
      <div className="products-page-title">
        Discover Our Product Line
      </div>

      <div className="products-container">
        {products.map(product => (
          <div key={product.id} className="product-box">
            <Link to={`/product/${product.id}`} className="product-link">
              <img 
                src={product.image} // Use path directly from public/
                alt={product.name} 
                className="product-image"
                onError={(e) => {
                  console.error(`Failed to load image: ${product.image}`);
                  e.target.src = '/placeholder.png'; // Fallback
                }}
              />
              <p className="product-name">{product.name}</p>
            </Link>
            {isAdmin && (
              <input
                type="radio"
                name="selectedProduct"
                value={product.id}
                onChange={() => setSelectedProductId(product.id)}
                checked={selectedProductId === product.id}
              />
            )}
          </div>
        ))}
      </div>

      {isAdmin && showAddForm && (
        <>
          <div className="add-product-backdrop" onClick={() => setShowAddForm(false)} />
          <div className="add-product-window">
            <div className="form-content">
              <h3>Add New Product</h3>
              {addError && <p className="error">{addError}</p>}
              <form onSubmit={handleAddSubmit}>
                <input
                  type="number"
                  placeholder="ID (unique number)"
                  value={addFormData.id}
                  onChange={(e) => setAddFormData({ ...addFormData, id: e.target.value })}
                  required
                />
                <input
                  type="text"
                  placeholder="Name"
                  value={addFormData.name}
                  onChange={(e) => setAddFormData({ ...addFormData, name: e.target.value })}
                  required
                />
                <div className="file-input-wrapper">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    required
                  />
                  {imagePreview && (
                    <img src={imagePreview} alt="Preview" className="image-preview" />
                  )}
                </div>
                <input
                  type="text"
                  placeholder="Price (e.g., $100)"
                  value={addFormData.price}
                  onChange={(e) => setAddFormData({ ...addFormData, price: e.target.value })}
                  required
                />
                <textarea
                  placeholder="Description"
                  value={addFormData.description}
                  onChange={(e) => setAddFormData({ ...addFormData, description: e.target.value })}
                  required
                />
                <input
                  type="text"
                  placeholder="Specs (comma-separated)"
                  value={addFormData.specs}
                  onChange={(e) => setAddFormData({ ...addFormData, specs: e.target.value })}
                />
                <button type="submit">Add Product</button>
                <button type="button" className="cancel" onClick={() => setShowAddForm(false)}>
                  Cancel
                </button>
              </form>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default ProductPage;