//  src/api/axios.js



import axios from 'axios';

// Get API base URL from environment variables with fallback
const getApiBaseUrl = () => {
  // In production, use the production API URL
  if (process.env.NODE_ENV === 'production') {
    return process.env.REACT_APP_API_URL || 'https://threed-e-commerce-backend.onrender.com/api';
  }
  // In development, use local API URL
  return process.env.REACT_APP_API_URL || 'http://localhost:5002/api';
};

//  axios instance with base URL
const api = axios.create({
  baseURL: getApiBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true,
  timeout: 10000 // 10 second timeout
});

// Add JWT token to all requests automatically
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle API errors consistently
api.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', {
      message: error.message,
      endpoint: error.config?.url,
      method: error.config?.method
    });
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      window.location = '/login'; // Redirect to login on token expiration
    }
    return Promise.reject(error);
  }
);

export default api;