import React, { useState, useEffect, useRef } from 'react';
import ProductModel from './ProductModel';
import LoginRegisterModal from './LoginRegisterModal';
import './App.css';
import './Home.css';

function Home() {
  const [showDetailModal, setShowDetailModal] = useState(false);
  const isLoggedIn = !!localStorage.getItem('token');
  const [showAuthModal, setShowAuthModal] = useState(!isLoggedIn);
  const [touchStart, setTouchStart] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isFloatingWindowOpen, setIsFloatingWindowOpen] = useState(false);
  const [isModelInteracting, setIsModelInteracting] = useState(false);
  const windowRef = useRef(null);
  const modelContainerRef = useRef(null);

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 768);
      setIsTablet(width > 768 && width <= 1024);
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  // Handle 3D model interaction
  useEffect(() => {
    if (!modelContainerRef.current) return;

    const handleInteractionStart = () => {
      setIsModelInteracting(true);
    };

    const handleInteractionEnd = () => {
      setIsModelInteracting(false);
    };

    const container = modelContainerRef.current;
    container.addEventListener('mousedown', handleInteractionStart);
    container.addEventListener('touchstart', handleInteractionStart);
    container.addEventListener('mouseup', handleInteractionEnd);
    container.addEventListener('touchend', handleInteractionEnd);
    container.addEventListener('mouseleave', handleInteractionEnd);

    return () => {
      container.removeEventListener('mousedown', handleInteractionStart);
      container.removeEventListener('touchstart', handleInteractionStart);
      container.removeEventListener('mouseup', handleInteractionEnd);
      container.removeEventListener('touchend', handleInteractionEnd);
      container.removeEventListener('mouseleave', handleInteractionEnd);
    };
  }, []);

  const handleTouchStart = (e) => {
    if (!isMobile && !isTablet) return;
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchEnd = (e) => {
    if ((!isMobile && !isTablet) || !touchStart) return;

    const endTouch = e.changedTouches[0].clientX;
    const diff = touchStart - endTouch;
    const swipeThreshold = 50;

    // Swipe right to left to show floating window (from right edge)
    if (diff > swipeThreshold && touchStart > window.innerWidth - 80) {
      setIsFloatingWindowOpen(true);
    }
    // Swipe left to right to hide floating window
    else if (diff < -swipeThreshold && isFloatingWindowOpen) {
      setIsFloatingWindowOpen(false);
    }

    setTouchStart(null);
  };

  const handleBackgroundClick = (e) => {
    if (windowRef.current && !windowRef.current.contains(e.target) && isFloatingWindowOpen) {
      setIsFloatingWindowOpen(false);
    }
  };

  const productDetails = {
    id: "home-featured-iphone14promax", // Unique ID for home page featured product
    _id: "home-featured-iphone14promax", // Ensure both id and _id are set
    name: "iPhone 14 Pro Max",
    price: 870,
    description: "The iPhone 14 Pro Max with a stunning display and long battery life.",
    image: "./iPhone-14-Pro-Max.png",
    specs: [
      "6.7-inch Super Retina XDR display",
      "A16 Bionic chip",
      "Pro camera system with improved low-light performance",
      "12-Camera Sensor Array",
      "iOS 16 with Dynamic Island"
    ]
  };

  const originalPrice = productDetails.price;
  const discountedPrice = isLoggedIn ? (originalPrice * 0.85).toFixed(2) : originalPrice;

  const addToCart = (product) => {
    console.log('🛒 HOME: Adding product to cart:', product);
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    console.log('🛒 HOME: Current cart before adding:', cart);

    // Ensure product has a unique identifier
    const productId = product._id || product.id;
    if (!productId) {
      console.error('Product missing ID:', product);
      alert('Error: Product ID is missing. Cannot add to cart.');
      return;
    }
    console.log('🛒 HOME: Using product ID:', productId);

    // Create a normalized product object with consistent ID structure
    const normalizedProduct = {
      ...product,
      id: productId,
      _id: productId,
      quantity: 1
    };

    const existingItem = cart.find(item => {
      const itemId = item._id || item.id;
      return itemId === productId;
    });

    if (existingItem) {
      console.log('🛒 HOME: Found existing item, incrementing quantity:', existingItem);
      existingItem.quantity += 1;
    } else {
      console.log('🛒 HOME: Adding new item to cart:', normalizedProduct);
      cart.push(normalizedProduct);
    }

    console.log('🛒 HOME: Final cart after adding:', cart);
    localStorage.setItem('cart', JSON.stringify(cart));

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));

    alert(`${product.name} added to cart!`);
  };

  return (
    <div
      className={`home-container ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={handleBackgroundClick}
    >
      <div
        className={`background-content ${showAuthModal ? 'blurred' : ''}`}
      >
        <div ref={modelContainerRef} className="model-container">
          <ProductModel />
        </div>

        {/* Swipe Indicator for Mobile/Tablet */}
        {(isMobile || isTablet) && !isFloatingWindowOpen && (
          <div className={`swipe-indicator ${isModelInteracting ? 'fade-out' : ''}`}>
            <div className="swipe-arrow">←</div>
            <span className="swipe-text">Swipe from right edge</span>
          </div>
        )}

        {/* Enhanced Floating Product Window */}
        <div
          ref={windowRef}
          className={`floating-product-window ${
            isMobile || isTablet
              ? (isFloatingWindowOpen ? 'visible' : 'hidden')
              : 'visible'
          } ${isTablet ? 'tablet-size' : ''}`}
        >
          <div className="product-window-header">
            <span className="featured-badge">Featured Product</span>
          </div>

          <div className="product-image-container">
            <img
              src={productDetails.image}
              alt={productDetails.name}
              className="product-window-img"
            />
          </div>

          <div className="product-window-content">
            <h3 className="product-window-title">{productDetails.name}</h3>

            <div className="price-container">
              {isLoggedIn ? (
                <div className="price-with-discount">
                  <span className="original-price">${originalPrice}</span>
                  <span className="discounted-price">${discountedPrice}</span>
                  <span className="discount-badge">15% OFF</span>
                </div>
              ) : (
                <span className="regular-price">${originalPrice}</span>
              )}
            </div>

            <p className="product-window-description">{productDetails.description}</p>

            <div className="product-actions">
              <button
                className="add-to-cart-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  addToCart(productDetails);
                }}
              >
                <span className="btn-icon">🛒</span>
                Add to Cart
              </button>

              <button
                className="view-details-btn"
                onClick={() => setShowDetailModal(true)}
              >
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>

      {showAuthModal && (
        <LoginRegisterModal onClose={() => setShowAuthModal(false)} />
      )}

      {showDetailModal && (
        <div className="enhanced-modal-overlay" onClick={() => setShowDetailModal(false)}>
          <div className="enhanced-product-modal" onClick={(e) => e.stopPropagation()}>
            <button className="enhanced-close-btn" onClick={() => setShowDetailModal(false)}>
              ×
            </button>

            <div className="modal-image-section">
              <img src={productDetails.image} alt={productDetails.name} className="enhanced-modal-image" />
            </div>

            <div className="modal-content-section">
              <div className="modal-header">
                <h2 className="modal-product-title">{productDetails.name}</h2>
                <div className="modal-price-container">
                  {isLoggedIn ? (
                    <div className="modal-price-with-discount">
                      <span className="modal-original-price">${originalPrice}</span>
                      <span className="modal-discounted-price">${discountedPrice}</span>
                      <span className="modal-discount-badge">15% OFF</span>
                    </div>
                  ) : (
                    <span className="modal-regular-price">${originalPrice}</span>
                  )}
                </div>
              </div>

              <div className="modal-specs-section">
                <h4 className="specs-title">Key Features</h4>
                <div className="enhanced-specs-list">
                  {productDetails.specs.map((spec, index) => (
                    <div key={index} className="enhanced-spec-item">
                      <span className="spec-icon">✓</span>
                      <span className="spec-text">{spec}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="modal-description-section">
                <h4 className="description-title">Description</h4>
                <p className="enhanced-modal-description">{productDetails.description}</p>
              </div>

              <div className="modal-actions">
                <button
                  className="enhanced-add-to-cart-btn"
                  onClick={() => {
                    addToCart(productDetails);
                    setShowDetailModal(false);
                  }}
                >
                  <span className="btn-icon">🛒</span>
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Home;