import React, { useState, useEffect } from 'react';
import './App.css';
import './About.css';

function About() {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 768);
      setIsTablet(width > 768 && width <= 1024);
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  return (
    <div className={`new-about-container ${isMobile ? 'mobile' : ''} ${isTablet ? 'tablet' : ''}`}>
      {/* Hero Section */}
      <div className="about-hero-section">
        <div className="hero-content-wrapper">
          <h1 className="about-main-title">
            <span className="title-gradient">About</span>
            <span className="title-accent">3D E-Commerce</span>
          </h1>
          <p className="hero-subtitle">
            Revolutionizing digital commerce through immersive 3D experiences
          </p>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="about-content-grid">
        {/* Vision Card */}
        <div className="about-card vision-card">
          <div className="card-header">
            <div className="card-icon">🚀</div>
            <h2 className="card-title">Our Vision</h2>
          </div>
          <div className="card-content">
            <p>
              We revolutionize tech presentation through immersive 3D web experiences.
              Specializing in photorealistic product visualization, we transform technical
              specifications into interactive journeys.
            </p>
            <p>
              Our WebGL-powered models allow users to explore every circuit and contour
              with browser-based precision, creating an unparalleled shopping experience.
            </p>
          </div>
        </div>

        {/* Technology Card */}
        <div className="about-card technology-card">
          <div className="card-header">
            <div className="card-icon">⚡</div>
            <h2 className="card-title">Technology</h2>
          </div>
          <div className="card-content">
            <p>
              From silicon architectures to wearable tech, we craft digital twins that
              outperform physical prototypes. Our real-time rendering pipeline delivers
              cinematic quality across all devices.
            </p>
            <div className="tech-features">
              <div className="feature-item">
                <span className="feature-icon">🎯</span>
                <span>Real-time 3D Rendering</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📱</span>
                <span>Cross-platform Compatibility</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔧</span>
                <span>Interactive Product Exploration</span>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Card */}
        <div className="about-card contact-card">
          <div className="card-header">
            <div className="card-icon">👨‍💻</div>
            <h2 className="card-title">Get In Touch</h2>
          </div>
          <div className="card-content">
            <div className="developer-info">
              <h3 className="developer-name">Med Amine Chouchane</h3>
              <p className="developer-role">Full-Stack Developer & 3D Specialist</p>
            </div>

            <div className="contact-methods">
              <a
                href="https://www.linkedin.com/in/chouchane-amine-932324320/"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-method linkedin"
              >
                <div className="method-icon">
                  <i className="fab fa-linkedin-in"></i>
                </div>
                <div className="method-info">
                  <span className="method-label">LinkedIn</span>
                  <span className="method-value">Chouchane Med Amine</span>
                </div>
              </a>

              <a
                href="https://www.instagram.com/_ami_nos_?igsh=bHQwdWFmdTM2dnk2"
                target="_blank"
                rel="noopener noreferrer"
                className="contact-method instagram"
              >
                <div className="method-icon">
                  <i className="fab fa-instagram"></i>
                </div>
                <div className="method-info">
                  <span className="method-label">Instagram</span>
                  <span className="method-value">_ami_nos_</span>
                </div>
              </a>

              <a
                href="mailto:<EMAIL>"
                className="contact-method email"
                title="<EMAIL>"
              >
                <div className="method-icon">
                  <i className="fas fa-envelope"></i>
                </div>
                <div className="method-info">
                  <span className="method-label">Email</span>
                  <span className="method-value">aminechouchane123@gma.....</span>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="about-stats-section">
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-number">100%</div>
            <div className="stat-label">WebGL Powered</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">3D</div>
            <div className="stat-label">Interactive Models</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">∞</div>
            <div className="stat-label">Possibilities</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default About;