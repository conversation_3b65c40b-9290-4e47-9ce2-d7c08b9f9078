<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 15px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-button {
            background: #00ffcc;
            color: black;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px;
            min-height: 44px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #00e6b8;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass { background: rgba(0, 255, 0, 0.2); }
        .fail { background: rgba(255, 0, 0, 0.2); }
        .info { background: rgba(0, 150, 255, 0.2); }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .test-section {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3D E-Commerce Mobile Responsiveness Test</h1>
        
        <div class="test-section">
            <h2>Device Information</h2>
            <div id="device-info" class="status info">
                <p><strong>Screen Size:</strong> <span id="screen-size"></span></p>
                <p><strong>Viewport:</strong> <span id="viewport"></span></p>
                <p><strong>Device Type:</strong> <span id="device-type"></span></p>
                <p><strong>Touch Support:</strong> <span id="touch-support"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>Touch Target Tests</h2>
            <p>All buttons should be at least 44px in height/width for proper touch interaction:</p>
            <button class="test-button" onclick="testTouchTarget(this)">Test Button 1</button>
            <button class="test-button" onclick="testTouchTarget(this)">Test Button 2</button>
            <button class="test-button" onclick="testTouchTarget(this)">Test Button 3</button>
            <div id="touch-results" class="status"></div>
        </div>

        <div class="test-section">
            <h2>Responsive Layout Test</h2>
            <p>Layout should adapt to different screen sizes:</p>
            <button class="test-button" onclick="testResponsiveLayout()">Test Layout</button>
            <div id="layout-results" class="status"></div>
        </div>

        <div class="test-section">
            <h2>Font Size Test</h2>
            <p>Text should be readable on mobile devices (minimum 16px):</p>
            <button class="test-button" onclick="testFontSizes()">Test Font Sizes</button>
            <div id="font-results" class="status"></div>
        </div>

        <div class="test-section">
            <h2>Navigation Test</h2>
            <p>Test mobile navigation functionality:</p>
            <button class="test-button" onclick="window.open('/', '_blank')">Open Main App</button>
            <div class="status info">
                <p>Manual tests to perform in the main app:</p>
                <ul>
                    <li>✓ Hamburger menu opens/closes smoothly</li>
                    <li>✓ All navigation links are touch-friendly</li>
                    <li>✓ 3D models respond to touch gestures</li>
                    <li>✓ Forms are easy to use on mobile</li>
                    <li>✓ Cart functionality works on touch devices</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Device detection and information
        function updateDeviceInfo() {
            const screenSize = `${screen.width} x ${screen.height}`;
            const viewport = `${window.innerWidth} x ${window.innerHeight}`;
            const isMobile = window.innerWidth <= 768;
            const hasTouch = 'ontouchstart' in window;
            
            document.getElementById('screen-size').textContent = screenSize;
            document.getElementById('viewport').textContent = viewport;
            document.getElementById('device-type').textContent = isMobile ? 'Mobile' : 'Desktop';
            document.getElementById('touch-support').textContent = hasTouch ? 'Yes' : 'No';
        }

        // Test touch target sizes
        function testTouchTarget(button) {
            const rect = button.getBoundingClientRect();
            const minSize = 44;
            const isValid = rect.width >= minSize && rect.height >= minSize;
            
            const results = document.getElementById('touch-results');
            results.className = `status ${isValid ? 'pass' : 'fail'}`;
            results.innerHTML = `
                <p><strong>Button Size:</strong> ${rect.width.toFixed(1)}px x ${rect.height.toFixed(1)}px</p>
                <p><strong>Status:</strong> ${isValid ? 'PASS - Meets touch target requirements' : 'FAIL - Too small for touch'}</p>
            `;
        }

        // Test responsive layout
        function testResponsiveLayout() {
            const width = window.innerWidth;
            const results = document.getElementById('layout-results');
            
            let status, message;
            if (width <= 480) {
                status = 'info';
                message = 'Extra small mobile layout active';
            } else if (width <= 768) {
                status = 'pass';
                message = 'Mobile layout active';
            } else if (width <= 1024) {
                status = 'pass';
                message = 'Tablet layout active';
            } else {
                status = 'pass';
                message = 'Desktop layout active';
            }
            
            results.className = `status ${status}`;
            results.innerHTML = `
                <p><strong>Current Width:</strong> ${width}px</p>
                <p><strong>Layout:</strong> ${message}</p>
            `;
        }

        // Test font sizes
        function testFontSizes() {
            const elements = document.querySelectorAll('p, h1, h2, h3, button');
            let minFontSize = Infinity;
            let allValid = true;
            
            elements.forEach(el => {
                const fontSize = parseFloat(window.getComputedStyle(el).fontSize);
                if (fontSize < minFontSize) minFontSize = fontSize;
                if (fontSize < 16) allValid = false;
            });
            
            const results = document.getElementById('font-results');
            results.className = `status ${allValid ? 'pass' : 'fail'}`;
            results.innerHTML = `
                <p><strong>Minimum Font Size:</strong> ${minFontSize.toFixed(1)}px</p>
                <p><strong>Status:</strong> ${allValid ? 'PASS - All text is readable' : 'FAIL - Some text may be too small'}</p>
            `;
        }

        // Initialize
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        
        // Auto-run some tests
        setTimeout(() => {
            testResponsiveLayout();
            testFontSizes();
        }, 1000);
    </script>
</body>
</html>
